# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Daily FX Compass is a real-time forex trading signal dashboard built with React, TypeScript, Vite, and Supabase. The application provides trading signals, technical analysis, and real-time currency pair tracking for major forex pairs (EUR/USD, GBP/USD, USD/JPY, USD/CHF, AUD/USD, NZD/USD, USD/CAD). The UI is in Ukrainian.

## Tech Stack

- **Frontend**: React 18 + TypeScript + Vite
- **Styling**: Tailwind CSS + shadcn/ui components
- **State Management**: TanStack Query (React Query) for server state
- **Backend**: Supabase (Edge Functions for API proxy)
- **Routing**: React Router v6
- **Forms**: React Hook Form + Zod validation
- **Charts**: Recharts

## Development Commands

```bash
# Start development server (runs on port 8080)
npm run dev

# Build for production
npm run build

# Build for development (with dev mode config)
npm run build:dev

# Lint code
npm run lint

# Preview production build
npm run preview
```

## Architecture

### Data Flow

1. **Real-time Forex Data Pipeline**:
   - `src/hooks/useForexData.ts` manages real-time data fetching via React Query
   - Updates every 30 seconds by default (configurable via `userSettings`)
   - `src/services/forexApi.ts` handles API calls to Supabase Edge Function
   - Supabase Edge Function (`supabase/functions/forex-proxy/index.ts`) proxies requests to external forex APIs with fallback chain
   - `src/services/cache.ts` provides in-memory caching (10s for rates, 5min for historical data)

2. **Forex API Proxy Chain** (in order of priority):
   - ExchangeRate.host (primary)
   - ExchangeRatesAPI.io (fallback)
   - Fixer.io (fallback)
   - Mock data (ultimate fallback)

3. **Component Hierarchy**:
   ```
   App.tsx (QueryClientProvider + Router)
   └── pages/Index.tsx (Tab navigation)
       ├── ForexDashboard.tsx (main dashboard)
       │   ├── SignalTable.tsx (currency pairs table)
       │   ├── DetailedAnalysis.tsx (technical analysis for selected pair)
       │   └── ActionPlan.tsx (trading recommendations)
       ├── SignalHistory.tsx (historical trades)
       └── Settings.tsx (user preferences)
   ```

### Key Services

- **forexApi** (`src/services/forexApi.ts`): Fetches and transforms forex rates from Supabase proxy, calculates technical indicators, handles rate normalization
- **cache** (`src/services/cache.ts`): In-memory cache with TTL, automatic cleanup every 5 minutes
- **backend** (`src/services/backend.ts`): Sync service for persisting signals/settings (currently local-first with queue)
- **userSettings** (`src/services/userSettings.ts`): LocalStorage-based settings management
- **signalHistory** (`src/services/signalHistory.ts`): LocalStorage-based trade history tracking

### Data Models

**ForexPair** (`src/lib/data.ts`):
- Core trading signal structure with pair, price, trends (H1/H4), support/resistance, entry/SL/TP
- Static initial data gets enriched with real-time prices from API

**ForexRate** (`src/services/forexApi.ts`):
- Real-time rate structure: bid, ask, price, change, changePercent, timestamp

### Supabase Integration

- Edge Functions deployed at: `https://[project-id].supabase.co/functions/v1/forex-proxy`
- Environment variables required:
  - `VITE_SUPABASE_URL`
  - `VITE_SUPABASE_PUBLISHABLE_KEY`
  - `VITE_SUPABASE_PROJECT_ID`
- Client configured in `src/integrations/supabase/client.ts`

## Important Development Notes

### Path Aliases

- `@/` maps to `./src/` (configured in `tsconfig.json` and `vite.config.ts`)
- Always use `@/` for imports within src directory

### TypeScript Configuration

- Relaxed strictness: `noImplicitAny: false`, `strictNullChecks: false`
- This is intentional for rapid prototyping - be mindful of potential runtime errors

### Real-time Updates

- `useForexData` hook uses React Query's `refetchInterval` for automatic updates
- Connection status is tracked and displayed in UI (`isConnected` state)
- Manual refresh available via `forceRefresh()` method

### Caching Strategy

- Forex rates: 10 seconds TTL (frequent updates needed)
- Historical data: 5 minutes TTL
- Analysis results: 10 minutes TTL
- Cache automatically cleans up expired entries every 5 minutes

### UI Components

- All UI components are from shadcn/ui located in `src/components/ui/`
- Custom components in `src/components/` (Header, ForexDashboard, SignalTable, etc.)
- Styling uses Tailwind utility classes with custom theme colors defined in `tailwind.config.ts`

## Common Development Patterns

### Adding a New Currency Pair

1. Add to `forexPairs` array in `src/lib/data.ts`
2. Update `CURRENCY_PAIRS` in `supabase/functions/forex-proxy/index.ts`
3. Add transformation logic in proxy function for the new pair

### Modifying Update Interval

- Update default in `useForexData` hook OR
- Change via Settings UI (stored in localStorage via `userSettings`)

### Adding New Technical Indicators

- Implement in `forexApi.calculateTechnicalIndicators()` method
- Update `updatePairWithRealData` in `useForexData` to apply new indicators
- Display in `DetailedAnalysis.tsx` component

## Project Structure

```
src/
├── components/       # React components
│   ├── ui/          # shadcn/ui components (Radix UI primitives)
│   └── *.tsx        # Custom components (ForexDashboard, SignalTable, etc.)
├── hooks/           # Custom React hooks (useForexData, use-toast, use-mobile)
├── integrations/    # External integrations
│   └── supabase/    # Supabase client and types
├── lib/             # Utilities and data
│   ├── data.ts      # Static forex pair data
│   └── utils.ts     # Helper functions (cn, etc.)
├── pages/           # Route pages (Index, NotFound)
├── services/        # Business logic services
│   ├── forexApi.ts      # API client
│   ├── cache.ts         # Caching layer
│   ├── backend.ts       # Backend sync
│   ├── userSettings.ts  # Settings management
│   └── signalHistory.ts # Trade history
├── App.tsx          # Root component
└── main.tsx         # Entry point

supabase/
└── functions/
    └── forex-proxy/ # Edge function for CORS-free forex API access
```

## ML API Integration

### Available ML Forex API (http://84.247.166.52:8001)

**Key Endpoints:**
- `GET /api/v1/forex/all` - Всі ціни валютних пар (MT5 real-time)
- `GET /api/v1/price/{symbol}` - Ціна конкретної пари з bid/ask/spread
- `GET /api/v1/predict/all` - ML прогнози для всіх пар
- `GET /api/v1/predict/{symbol}` - ML прогноз для конкретної пари (RandomForest)
- `GET /api/v1/system/status` - Статус MT5, моделей, database
- `GET /api/v1/market/summary` - Загальна статистика (точність моделей)
- `GET /api/v1/training/status` - Прогрес навчання ML моделей
- `GET /dashboard` - Вбудований web-інтерфейс

**Supported Pairs:** EURUSD, GBPUSD, USDJPY, AUDUSD, USDCAD, EURGBP, EURJPY, GBPJPY

**ML Features:**
- Real-time MT5 ціни (bid/ask/spread)
- ML прогнози з confidence level (RandomForest models)
- Accuracy tracking для кожної моделі
- Auto-retraining кожні 6 годин

**Integration Plan:** See `ML_API_INTEGRATION.md` for detailed implementation guide

### Current Implementation
- Uses Supabase Edge Function as proxy to public forex APIs (unreliable)
- Should be migrated to ML API for:
  - Real MT5 prices instead of outdated public API data
  - ML predictions with confidence levels
  - Better reliability and accuracy

## Deployment Notes

- Built with Lovable platform (original project URL in README.md)
- Supabase Edge Functions must be deployed separately
- Environment variables must be set in deployment environment
- Static assets served from Vite build output
- **TODO**: Migrate from Supabase proxy to direct ML API integration
