# 🐛 Bug Fix - Null Reference Error

## Проблема
```
TypeError: Cannot read properties of null (reading 'toFixed')
at SignalTable.tsx:110:57
```

## Причина
ML API може не повертати прогнози для деяких валютних пар (модель ще навчається або недоступна). Код намагався викликати `toFixed()` на `null` значенні.

## Виправлення

### 1. SignalTable.tsx (рядок 104)
**Було:**
```typescript
{prediction ? (
  <span>{prediction.predicted_price.toFixed(isJPY ? 2 : 4)}</span>
) : (...)}
```

**Стало:**
```typescript
{prediction && prediction.predicted_price != null && prediction.change_percent != null ? (
  <span>{prediction.predicted_price.toFixed(isJPY ? 2 : 4)}</span>
) : (...)}
```

### 2. MLPredictionCard.tsx (початок компонента)
**Додано:**
```typescript
if (!prediction || prediction.current_price == null || prediction.predicted_price == null) {
  return null;
}
```

## Результат
✅ Додано перевірку на `null/undefined` для всіх числових полів прогнозу
✅ Якщо прогноз недоступний, показується "-" замість помилки
✅ HMR успішно оновив компоненти без перезавантаження сторінки

## Тестування
1. Відкрийте http://localhost:8080/
2. Перевірте, що таблиця відображається без помилок
3. Для пар без ML прогнозів має показуватись "-"
4. Для пар з прогнозами - ціна і change_percent з кольором

## Примітка
Це нормально, що не всі пари мають ML прогнози одразу. ML API може:
- Ще навчати моделі для нових пар (EUR/GBP, EUR/JPY, GBP/JPY)
- Мати тимчасові проблеми з підключенням до MT5
- Перебувати в процесі retraining

Система тепер коректно обробляє всі ці випадки.
