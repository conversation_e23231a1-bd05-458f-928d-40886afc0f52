# ML API Integration Guide

## 🤖 Інтеграція з ML Forex API

### API Endpoints (http://*************:8001)

#### 🌐 Основні endpoints

**GET /** - Головна сторінка API
- URL: `http://*************:8001/`

**GET /dashboard** - Веб-інтерфейс з реальним часом
- URL: `http://*************:8001/dashboard`

#### 💱 Ціни та дані

**GET /api/v1/price/{symbol}** - Поточна ціна валютної пари
```typescript
// URL: http://*************:8001/api/v1/price/EURUSD
// Response:
{
  "symbol": "EURUSD",
  "price": 1.17225,
  "bid": 1.17222,
  "ask": 1.17228,
  "spread": 0.00006,
  "timestamp": "2025-09-29T23:09:18",
  "source": "mt5"
}
```

**GET /api/v1/forex/all** - Ціни всіх валютних пар одразу
```typescript
// URL: http://*************:8001/api/v1/forex/all
// Response:
{
  "prices": [
    {"symbol": "EURUSD", "price": 1.17225, "bid": 1.17222, "ask": 1.17228},
    {"symbol": "GBPUSD", "price": 1.34267, "bid": 1.34264, "ask": 1.3427},
    // ... всі 8 пар
  ],
  "count": 8,
  "timestamp": "2025-09-29T20:09:32"
}
```

#### 🤖 Машинне навчання

**GET /api/v1/predict/{symbol}** - ML прогноз ціни
```typescript
// URL: http://*************:8001/api/v1/predict/EURUSD
// Response:
{
  "symbol": "EURUSD",
  "current_price": 1.17225,
  "predicted_price": 1.17245,
  "change_percent": 0.017,
  "confidence": 85.2,
  "model_type": "RandomForest"
}
```

**GET /api/v1/predict/all** - Прогнози ВСІХ пар одразу
```typescript
// URL: http://*************:8001/api/v1/predict/all
// Response:
{
  "predictions": [
    {
      "symbol": "EURUSD",
      "current_price": 1.17225,
      "predicted_price": 1.17245,
      "change_percent": 0.017,
      "confidence": 85.2
    },
    // ... всі пари
  ],
  "timestamp": "2025-09-29T20:09:32"
}
```

**GET /api/v1/training/status** - Статус навчання моделей
```typescript
// URL: http://*************:8001/api/v1/training/status
// Response:
{
  "is_training": false,
  "current_symbol": null,
  "current_step": 8,
  "total_steps": 8,
  "progress_percent": 100,
  "last_training_start": "2025-09-29T19:31:15",
  "training_results": {
    "EURUSD": {"status": "success", "accuracy": 87.5}
  }
}
```

#### 📊 Системна інформація

**GET /api/v1/system/status** - Статус системи
```typescript
// URL: http://*************:8001/api/v1/system/status
// Response:
{
  "mt5_connected": true,
  "data_collector_running": true,
  "auto_trainer_running": true,
  "database_records": 20699,
  "active_models": 8,
  "server_uptime": "2:15:42"
}
```

**GET /api/v1/market/summary** - Загальна статистика ринку
```typescript
// URL: http://*************:8001/api/v1/market/summary
// Response:
{
  "total_pairs": 8,
  "active_pairs": 8,
  "total_predictions": 156,
  "average_accuracy": 85.7,
  "data_freshness": "real-time"
}
```

#### 🔧 Адміністративні

**POST /api/v1/admin/retrain/{symbol}** - Перенавчання моделі
- URL: `http://*************:8001/api/v1/admin/retrain/EURUSD`
- Method: POST

**POST /api/v1/admin/force-update** - Примусове оновлення даних
- URL: `http://*************:8001/api/v1/admin/force-update`
- Method: POST

#### 📚 Документація

**GET /docs** - Swagger UI
- URL: `http://*************:8001/docs`

**GET /redoc** - ReDoc
- URL: `http://*************:8001/redoc`

---

## 💎 Підтримувані валютні пари

1. **EURUSD** - Євро/Долар США
2. **GBPUSD** - Фунт/Долар США
3. **USDJPY** - Долар США/Японська єна
4. **AUDUSD** - Австралійський долар/Долар США
5. **USDCAD** - Долар США/Канадський долар
6. **EURGBP** - Євро/Фунт
7. **EURJPY** - Євро/Японська єна
8. **GBPJPY** - Фунт/Японська єна

---

## 🚀 План інтеграції

### Фаза 1: Базова інтеграція
1. Створити новий сервіс `src/services/mlForexApi.ts`
2. Замінити Supabase Edge Function на прямі запити до ML API
3. Додати типи для ML prediction responses
4. Оновити `useForexData` hook для роботи з ML API

### Фаза 2: ML Predictions UI
1. Додати компонент `MLPredictionCard` для відображення прогнозів
2. Показувати `confidence` level для кожної пари
3. Візуалізація `predicted_price` vs `current_price`
4. Індикатор `change_percent` з кольоровим кодуванням

### Фаза 3: Розширена аналітика
1. Інтеграція з `/api/v1/training/status` для моніторингу моделей
2. Dashboard з `/api/v1/system/status` та `/api/v1/market/summary`
3. Історія точності прогнозів
4. Алерти при високому confidence (>80%)

### Фаза 4: Real-time Features
1. WebSocket підключення для live updates
2. Автоматичні оповіщення при сильних сигналах
3. Live графіки predicted vs actual
4. Інтеграція embedded `/dashboard` у iframe

---

## 🔧 Приклад коду інтеграції

### Новий ML API Service

```typescript
// src/services/mlForexApi.ts
const ML_API_BASE = 'http://*************:8001/api/v1';

export interface MLPrice {
  symbol: string;
  price: number;
  bid: number;
  ask: number;
  spread: number;
  timestamp: string;
  source: 'mt5';
}

export interface MLPrediction {
  symbol: string;
  current_price: number;
  predicted_price: number;
  change_percent: number;
  confidence: number;
  model_type: string;
}

export interface MLAllPricesResponse {
  prices: MLPrice[];
  count: number;
  timestamp: string;
}

export interface MLAllPredictionsResponse {
  predictions: MLPrediction[];
  timestamp: string;
}

class MLForexApiService {
  async getAllPrices(): Promise<MLAllPricesResponse> {
    const response = await fetch(`${ML_API_BASE}/forex/all`);
    if (!response.ok) throw new Error('Failed to fetch prices');
    return response.json();
  }

  async getPrice(symbol: string): Promise<MLPrice> {
    const response = await fetch(`${ML_API_BASE}/price/${symbol}`);
    if (!response.ok) throw new Error(`Failed to fetch ${symbol}`);
    return response.json();
  }

  async getAllPredictions(): Promise<MLAllPredictionsResponse> {
    const response = await fetch(`${ML_API_BASE}/predict/all`);
    if (!response.ok) throw new Error('Failed to fetch predictions');
    return response.json();
  }

  async getPrediction(symbol: string): Promise<MLPrediction> {
    const response = await fetch(`${ML_API_BASE}/predict/${symbol}`);
    if (!response.ok) throw new Error(`Failed to predict ${symbol}`);
    return response.json();
  }

  async getSystemStatus() {
    const response = await fetch(`${ML_API_BASE}/system/status`);
    if (!response.ok) throw new Error('Failed to fetch system status');
    return response.json();
  }

  async getMarketSummary() {
    const response = await fetch(`${ML_API_BASE}/market/summary`);
    if (!response.ok) throw new Error('Failed to fetch market summary');
    return response.json();
  }
}

export const mlForexApi = new MLForexApiService();
```

### Оновлений Hook

```typescript
// src/hooks/useMLForexData.ts
import { useQuery } from '@tanstack/react-query';
import { mlForexApi } from '@/services/mlForexApi';

export const useMLForexData = (updateInterval: number = 30000) => {
  // Fetch prices
  const pricesQuery = useQuery({
    queryKey: ['mlForexPrices'],
    queryFn: () => mlForexApi.getAllPrices(),
    refetchInterval: updateInterval,
  });

  // Fetch predictions
  const predictionsQuery = useQuery({
    queryKey: ['mlForexPredictions'],
    queryFn: () => mlForexApi.getAllPredictions(),
    refetchInterval: updateInterval,
  });

  return {
    prices: pricesQuery.data?.prices || [],
    predictions: predictionsQuery.data?.predictions || [],
    isLoading: pricesQuery.isLoading || predictionsQuery.isLoading,
    error: pricesQuery.error || predictionsQuery.error,
  };
};
```

---

## ⚠️ Важливі зауваження

1. **CORS**: Переконайтесь, що ML API дозволяє CORS запити з вашого домену
2. **Rate Limiting**: ML API може мати обмеження на кількість запитів
3. **Error Handling**: Додати fallback на mock дані при недоступності ML API
4. **Security**: Розглянути додавання API ключа для авторизації
5. **Performance**: Кешувати predictions на 30-60 секунд (вони не змінюються так часто)
