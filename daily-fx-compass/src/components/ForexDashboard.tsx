import { useState, useMemo, useEffect } from "react";
import { SignalTable } from "@/components/SignalTable";
import { DetailedAnalysis } from "@/components/DetailedAnalysis";
import { ActionPlan } from "@/components/ActionPlan";
import { MLSystemStatus } from "@/components/MLSystemStatus";
import { BacktestResults } from "@/components/BacktestResults";
import { forexPairs, ForexPair } from "@/lib/data";
import { useMLForexData } from "@/hooks/useMLForexData";
import { userSettings } from "@/services/userSettings";
import { Badge } from "@/components/ui/badge";
import { AlertCircle, Wifi, WifiOff, Brain } from "lucide-react";

export const ForexDashboard = () => {
  const [selectedPair, setSelectedPair] = useState<ForexPair | null>(forexPairs[0]);
  const [currentTime, setCurrentTime] = useState(new Date());

  // Get real-time ML forex data
  const updateInterval = userSettings.getUpdateIntervalMs();
  console.log('⏱️ Update interval:', updateInterval, 'ms');

  const { isLoading, error, lastUpdate, isConnected, wsConnected, refreshPair, predictions, prices } = useMLForexData(
    updateInterval
  );

  const eurusdPrice = prices.find(p => p.symbol === 'EURUSD');
  console.log('📊 Prices:', prices.length, 'EURUSD:', eurusdPrice?.price);

  // Update current time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000);

    return () => clearInterval(timer);
  }, []);

  // Priority refresh for selected pair (2x faster than base interval)
  useEffect(() => {
    if (!selectedPair || wsConnected) return;

    const priorityInterval = Math.max(userSettings.getUpdateIntervalMs() / 2, 1000);
    const timer = setInterval(() => {
      refreshPair(selectedPair.pair);
    }, priorityInterval);

    return () => clearInterval(timer);
  }, [selectedPair, refreshPair, wsConnected]);

  // Update pairs with ML real-time data
  const updatedPairs = useMemo(() => {
    const updated = forexPairs.map(pair => {
      const normalizedSymbol = pair.pair.replace('/', '');
      const priceData = prices.find(p => p.symbol === normalizedSymbol);
      const predictionData = predictions.find(p => p.symbol === normalizedSymbol);

      if (!priceData) return pair;

      const isJPY = pair.pair.includes('JPY');
      const decimals = isJPY ? 2 : 4;
      const midPrice = (priceData.bid + priceData.ask) / 2;
      const newPrice = midPrice.toFixed(decimals);

      // Calculate support/resistance
      const support = (midPrice * 0.998).toFixed(decimals);
      const resistance = (midPrice * 1.002).toFixed(decimals);

      // Update trends based on prediction
      let trendH1 = pair.trendH1;
      let trendH4 = pair.trendH4;
      let signal = pair.signal;
      let probability = pair.probability;

      if (predictionData) {
        const changePercent = predictionData.change_percent;
        if (changePercent > 0.05) {
          trendH1 = "BULLISH";
          trendH4 = changePercent > 0.15 ? "BULLISH" : "NEUTRAL";
          signal = "BUY";
        } else if (changePercent < -0.05) {
          trendH1 = "BEARISH";
          trendH4 = changePercent < -0.15 ? "BEARISH" : "NEUTRAL";
          signal = "SELL";
        } else {
          trendH1 = "NEUTRAL";
          trendH4 = "NEUTRAL";
        }
        probability = Math.round(predictionData.confidence);
      }

      // Calculate entry/SL/TP
      const spread = priceData.spread || 0.0001;

      let entry, sl, tp, pendingOrder;
      if (signal === "BUY") {
        entry = (midPrice + spread).toFixed(decimals);
        sl = (midPrice - (midPrice * 0.005)).toFixed(decimals);
        tp = (midPrice + (midPrice * 0.01)).toFixed(decimals);
        pendingOrder = `BUY LIMIT ${(midPrice - spread * 2).toFixed(decimals)}`;
      } else {
        entry = (midPrice - spread).toFixed(decimals);
        sl = (midPrice + (midPrice * 0.005)).toFixed(decimals);
        tp = (midPrice - (midPrice * 0.01)).toFixed(decimals);
        pendingOrder = `SELL STOP ${(midPrice - spread * 2).toFixed(decimals)}`;
      }

      return {
        ...pair,
        price: newPrice,
        support,
        resistance,
        trendH1,
        trendH4,
        signal,
        entry,
        sl,
        tp,
        probability,
        pendingOrder,
      };
    });

    return updated;
  }, [prices, predictions]);

  const formatLastUpdate = (timestamp: string | number) => {
    if (!timestamp) return "Немає даних";
    const date = typeof timestamp === 'string' ? new Date(timestamp) : new Date(timestamp);
    return date.toLocaleTimeString('uk-UA', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  // Find updated version of selected pair
  const selectedPairUpdated = useMemo(() => {
    if (!selectedPair) return null;
    return updatedPairs.find(p => p.pair === selectedPair.pair) || selectedPair;
  }, [selectedPair, updatedPairs]);

  // Find prediction for selected pair
  const selectedPrediction = useMemo(() => {
    if (!selectedPair) return null;
    const normalizedSymbol = selectedPair.pair.replace('/', '');
    return predictions.find(p => p.symbol === normalizedSymbol);
  }, [selectedPair, predictions]);

  return (
    <div className="space-y-8">
      {/* Daily Overview */}
      <div className="bg-gradient-to-br from-card to-card/80 rounded-lg p-6 border border-border shadow-lg">
        <div className="mb-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h2 className="text-xl font-bold text-foreground mb-2 flex items-center gap-2">
                <Brain className="h-5 w-5 text-primary" />
                ML Forex Dashboard • {currentTime.toLocaleDateString('uk-UA', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })} о {currentTime.toLocaleTimeString('uk-UA', {
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </h2>
              <p className="text-muted-foreground text-sm">
                Прогнози на основі ML моделей • MT5 real-time дані
              </p>
            </div>
            
            {/* Connection Status */}
            <div className="flex flex-col items-end gap-2">
              <div className="flex items-center gap-2">
                {isConnected ? (
                  <Wifi className="h-4 w-4 text-profit" />
                ) : (
                  <WifiOff className="h-4 w-4 text-loss" />
                )}
                <Badge
                  className={isConnected ? "bg-profit text-profit-foreground" : "bg-loss text-loss-foreground"}
                >
                  {isConnected ? "З'ЄДНАНО" : "ВІДКЛЮЧЕНО"}
                </Badge>
              </div>

              {/* WebSocket Status Indicator */}
              {wsConnected && (
                <div className="flex items-center gap-2">
                  <div className="h-2 w-2 rounded-full bg-profit animate-pulse" />
                  <Badge className="bg-primary text-primary-foreground text-xs">
                    WebSocket
                  </Badge>
                </div>
              )}
              
              {error && (
                <div className="flex items-center gap-1 text-loss text-xs">
                  <AlertCircle className="h-3 w-3" />
                  <span>Помилка: {error.message || String(error)}</span>
                </div>
              )}
              
              <p className="text-xs text-muted-foreground">
                Останнє оновлення: {formatLastUpdate(lastUpdate)}
              </p>
              
              {isLoading && (
                <Badge className="bg-primary text-primary-foreground animate-pulse">
                  ОНОВЛЕННЯ...
                </Badge>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* ML System Status */}
      <MLSystemStatus />

      {/* Backtest Results */}
      <BacktestResults />

      {/* Signal Table */}
      <SignalTable
        pairs={updatedPairs}
        selectedPair={selectedPair}
        onPairSelect={setSelectedPair}
        isLoading={isLoading}
        predictions={predictions}
      />

      {/* Detailed Analysis */}
      <DetailedAnalysis pair={selectedPairUpdated} prediction={selectedPrediction} />

      {/* Action Plan */}
      <ActionPlan pairs={updatedPairs} predictions={predictions} />
    </div>
  );
};