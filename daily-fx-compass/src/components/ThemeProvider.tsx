import { useEffect, useState } from "react";
import { userSettings } from "@/services/userSettings";

export const ThemeProvider = ({ children }: { children: React.ReactNode }) => {
  const [theme, setTheme] = useState<'light' | 'dark' | 'auto'>(userSettings.getTheme());

  useEffect(() => {
    const applyTheme = (themeSetting: 'light' | 'dark' | 'auto') => {
      const root = document.documentElement;

      // Remove both classes first
      root.classList.remove('light', 'dark');

      let appliedTheme: string;
      if (themeSetting === 'auto') {
        // Check system preference
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        appliedTheme = prefersDark ? 'dark' : 'light';
        root.classList.add(appliedTheme);
      } else {
        appliedTheme = themeSetting;
        root.classList.add(themeSetting);
      }

      console.log('🎨 Theme applied:', appliedTheme, '| Setting:', themeSetting);
    };

    // Apply theme on mount
    applyTheme(theme);

    // Listen for settings changes
    const handleSettingsChange = (event: Event) => {
      const customEvent = event as CustomEvent;
      const newTheme = customEvent.detail.theme;
      setTheme(newTheme);
      applyTheme(newTheme);
    };

    window.addEventListener('userSettingsChanged', handleSettingsChange);

    // Listen for system theme changes if 'auto' is selected
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleSystemThemeChange = () => {
      if (theme === 'auto') {
        applyTheme('auto');
      }
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);

    return () => {
      window.removeEventListener('userSettingsChanged', handleSettingsChange);
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
    };
  }, [theme]);

  return <>{children}</>;
};
