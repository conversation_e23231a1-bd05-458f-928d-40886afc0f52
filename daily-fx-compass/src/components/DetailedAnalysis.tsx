import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, TrendingUp, Globe, BarChart3, BarChart, Brain, ChevronDown, ChevronUp } from "lucide-react";
import { ForexPair } from "@/lib/data";
import { MLPrediction } from "@/services/mlForexApi";
import { MLPredictionCard } from "@/components/MLPredictionCard";

const timeFrameAnalysis = {
  "EUR/USD": {
    H4: "Бичачий тренд з пробоєм над 1.0870. Ціль 1.0920-1.0950.",
    H1: "Консолідація. Очікуємо пробою вгору після 09:00 GMT.",
    M15: "Короткострокова корекція завершена. Готовність до покупок."
  },
  "GBP/USD": {
    H4: "Ведмежий канал. Тиск продажів зберігається.",
    H1: "Відскок від 1.2650 не переконливий. Продовжуємо продажі.",
    M15: "Локальна підтримка 1.2645 під тиском."
  }
};

const newsBackground = [
  {
    time: "08:30",
    event: "Дані інфляції Єврозони",
    impact: "HIGH",
    forecast: "2.3%",
    previous: "2.4%"
  },
  {
    time: "14:30", 
    event: "US Initial Jobless Claims",
    impact: "MEDIUM",
    forecast: "220K",
    previous: "218K"
  },
  {
    time: "20:00",
    event: "Виступ голови ФРС",
    impact: "HIGH",
    forecast: "-",
    previous: "-"
  }
];

const hourlyScenarios = [
  {
    time: "07:30-08:30",
    description: "Відкриття Європейської сесії. Низька волатільність до новин о 08:30.",
    action: "Чекати пробою ключових рівнів після новин."
  },
  {
    time: "09:00-10:00", 
    description: "Пік активності EUR/USD та GBP/USD. Очікуємо імпульсні рухи.",
    action: "Активна торгівля на пробоях."
  },
  {
    time: "11:00-13:00",
    description: "Консолідація перед Американською сесією.",
    action: "Скалпінг в діапазонах."
  },
  {
    time: "14:30-16:00",
    description: "Американські новини. Волатільність USD пар.",
    action: "Обережно з USD парами."
  },
  {
    time: "17:00-19:00",
    description: "Перекриття сесій. Максимальна ліквідність.",
    action: "Основний торговий період."
  },
  {
    time: "20:00-22:00",
    description: "Завершення дня. Фіксація прибутків.",
    action: "Закриття позицій до кінця дня."
  }
];

const getImpactBadge = (impact: string) => {
  switch (impact) {
    case "HIGH":
      return <Badge className="bg-loss text-loss-foreground">HIGH</Badge>;
    case "MEDIUM":
      return <Badge className="bg-primary text-primary-foreground">MEDIUM</Badge>;
    default:
      return <Badge className="bg-neutral text-neutral-foreground">LOW</Badge>;
  }
};

interface DetailedAnalysisProps {
  pair: ForexPair | null;
  prediction?: MLPrediction | null;
}

export const DetailedAnalysis = ({ pair, prediction }: DetailedAnalysisProps) => {
  const [isCollapsed, setIsCollapsed] = useState(false);

  if (!pair) {
    return (
      <Card className="bg-gradient-to-br from-card to-card/80 border-border shadow-lg">
        <CardHeader
          className="cursor-pointer hover:bg-muted/50 transition-colors"
          onClick={() => setIsCollapsed(!isCollapsed)}
        >
          <CardTitle className="flex items-center gap-2">
            <BarChart className="h-5 w-5 text-primary" />
            Детальний Аналіз
            {isCollapsed ? (
              <ChevronDown className="h-4 w-4 text-muted-foreground" />
            ) : (
              <ChevronUp className="h-4 w-4 text-muted-foreground" />
            )}
          </CardTitle>
        </CardHeader>
        {!isCollapsed && (
          <CardContent>
            <p className="text-muted-foreground">Оберіть пару з таблиці, щоб побачити детальний аналіз.</p>
          </CardContent>
        )}
      </Card>
    );
  }

  return (
    <Card className="bg-gradient-to-br from-card to-card/80 border-border shadow-lg">
      <CardHeader
        className="cursor-pointer hover:bg-muted/50 transition-colors"
        onClick={() => setIsCollapsed(!isCollapsed)}
      >
        <CardTitle className="flex items-center gap-2">
          <BarChart className="h-5 w-5 text-primary" />
          Детальний Аналіз: {pair.pair}
          {isCollapsed ? (
            <ChevronDown className="h-4 w-4 text-muted-foreground" />
          ) : (
            <ChevronUp className="h-4 w-4 text-muted-foreground" />
          )}
        </CardTitle>
      </CardHeader>
      {!isCollapsed && (
        <CardContent className="space-y-4">
        <Tabs defaultValue="technical" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="technical">Технічний</TabsTrigger>
            <TabsTrigger value="ml">
              <Brain className="h-3 w-3 mr-1" />
              ML Прогноз
            </TabsTrigger>
            <TabsTrigger value="news">Новини</TabsTrigger>
            <TabsTrigger value="scenarios">Сценарії</TabsTrigger>
            <TabsTrigger value="timeframes">Таймфрейми</TabsTrigger>
          </TabsList>

          {/* ML Prediction Tab */}
          <TabsContent value="ml" className="space-y-4">
            {prediction ? (
              <MLPredictionCard prediction={prediction} />
            ) : (
              <Card className="border-muted">
                <CardContent className="pt-6">
                  <div className="text-center text-muted-foreground">
                    <Brain className="h-12 w-12 mx-auto mb-3 opacity-50" />
                    <p>ML прогноз для цієї пари недоступний</p>
                    <p className="text-xs mt-2">Модель може бути в процесі навчання</p>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="technical" className="space-y-4">
            <div>
              <h3 className="font-semibold text-foreground mb-2">Технічний Аналіз</h3>
              <p className="text-muted-foreground text-sm">
                Тренд на H4: <span className="font-bold">{pair.trendH4}</span>, Тренд на H1: <span className="font-bold">{pair.trendH1}</span>.
                Сигнал <span className="font-bold">{pair.signal}</span> з ймовірністю <span className="font-bold">{pair.probability}%</span>.
              </p>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-foreground mb-2">Ключові Рівні</h4>
                <p className="text-muted-foreground text-sm">
                  Підтримка: <span className="font-mono text-primary">{pair.support}</span>
                </p>
                <p className="text-muted-foreground text-sm">
                  Опір: <span className="font-mono text-primary">{pair.resistance}</span>
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold text-foreground mb-2">Поточна Ціна</h4>
                <p className="text-2xl font-mono text-primary">{pair.price}</p>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="news" className="space-y-4">
            <h3 className="font-semibold text-foreground flex items-center gap-2">
              <Globe className="h-4 w-4" />
              Новинний Фон
            </h3>
            <div className="space-y-3">
              {newsBackground.map((news, index) => (
                <div key={index} className="border rounded-lg p-3 bg-card/50">
                  <div className="flex justify-between items-start mb-2">
                    <span className="font-medium text-primary">{news.time}</span>
                    {getImpactBadge(news.impact)}
                  </div>
                  <h4 className="font-medium text-foreground">{news.event}</h4>
                  <div className="flex gap-4 text-sm text-muted-foreground mt-1">
                    <span>Прогноз: {news.forecast}</span>
                    <span>Попередній: {news.previous}</span>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="scenarios" className="space-y-4">
            <h3 className="font-semibold text-foreground flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Сценарії по Годинах
            </h3>
            <div className="space-y-3">
              {hourlyScenarios.map((scenario, index) => (
                <div key={index} className="border rounded-lg p-3 bg-card/50">
                  <div className="flex justify-between items-start mb-2">
                    <span className="font-medium text-primary">{scenario.time}</span>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">{scenario.description}</p>
                  <p className="text-sm font-medium text-foreground">
                    💡 {scenario.action}
                  </p>
                </div>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="timeframes" className="space-y-4">
            <h3 className="font-semibold text-foreground flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Мультитаймфрейм Аналіз
            </h3>
            <div className="space-y-3">
              <div className="border rounded-lg p-3 bg-card/50">
                <h4 className="font-medium text-primary mb-2">H4 Таймфрейм</h4>
                <p className="text-sm text-muted-foreground">
                  {timeFrameAnalysis[pair.pair as keyof typeof timeFrameAnalysis]?.H4 || 
                   "Бичачий тренд з пробоєм над ключовими рівнями. Ціль наступний рівень опору."}
                </p>
              </div>
              
              <div className="border rounded-lg p-3 bg-card/50">
                <h4 className="font-medium text-primary mb-2">H1 Таймфрейм</h4>
                <p className="text-sm text-muted-foreground">
                  {timeFrameAnalysis[pair.pair as keyof typeof timeFrameAnalysis]?.H1 || 
                   "Консолідація в межах діапазону. Очікуємо пробою після ключових новин."}
                </p>
              </div>
              
              <div className="border rounded-lg p-3 bg-card/50">
                <h4 className="font-medium text-primary mb-2">M15 Таймфрейм</h4>
                <p className="text-sm text-muted-foreground">
                  {timeFrameAnalysis[pair.pair as keyof typeof timeFrameAnalysis]?.M15 || 
                   "Короткострокова корекція. Слідувати загальному тренду старших таймфреймів."}
                </p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
        </CardContent>
      )}
    </Card>
  );
};