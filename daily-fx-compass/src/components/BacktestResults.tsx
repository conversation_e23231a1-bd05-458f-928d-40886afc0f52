import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { BarChart3, TrendingUp, Target, RefreshCw, CheckCircle2, XCircle, ChevronDown, ChevronUp } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { mlForexApi, MLBacktestResult } from "@/services/mlForexApi";
import { forexPairs } from "@/lib/data";

export const BacktestResults = () => {
  const [selectedSymbol, setSelectedSymbol] = useState("EURUSD");
  const [days, setDays] = useState(30);
  const [isCollapsed, setIsCollapsed] = useState(false);

  const { data: backtest, isLoading, error, refetch } = useQuery({
    queryKey: ['backtest', selectedSymbol, days],
    queryFn: () => mlForexApi.getBacktest(selectedSymbol, days),
    staleTime: 60000, // Cache for 1 minute
  });

  const getAccuracyColor = (accuracy: number) => {
    if (accuracy >= 70) return "text-profit";
    if (accuracy >= 60) return "text-primary";
    return "text-loss";
  };

  const getAccuracyBg = (accuracy: number) => {
    if (accuracy >= 70) return "bg-profit/10 border-profit";
    if (accuracy >= 60) return "bg-primary/10 border-primary";
    return "bg-loss/10 border-loss";
  };

  return (
    <Card className="bg-gradient-to-br from-card to-card/80 border-border shadow-lg">
      <CardHeader
        className="cursor-pointer hover:bg-muted/50 transition-colors"
        onClick={() => setIsCollapsed(!isCollapsed)}
      >
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-primary" />
            Бектестинг ML Моделі
            {isCollapsed ? (
              <ChevronDown className="h-4 w-4 text-muted-foreground" />
            ) : (
              <ChevronUp className="h-4 w-4 text-muted-foreground" />
            )}
          </CardTitle>
          {!isCollapsed && (
            <div className="flex items-center gap-2" onClick={(e) => e.stopPropagation()}>
              <Select value={selectedSymbol} onValueChange={setSelectedSymbol}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {forexPairs.map((pair) => (
                    <SelectItem key={pair.pair} value={pair.pair.replace('/', '')}>
                      {pair.pair}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={days.toString()} onValueChange={(v) => setDays(Number(v))}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">7 днів</SelectItem>
                  <SelectItem value="30">30 днів</SelectItem>
                  <SelectItem value="90">90 днів</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" size="sm" onClick={() => refetch()}>
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      </CardHeader>

      {!isCollapsed && (
        <CardContent className="space-y-4">
        {isLoading && (
          <div className="text-center py-8 text-muted-foreground">
            Завантаження даних бектесту...
          </div>
        )}

        {error && (
          <div className="text-center py-8 text-loss">
            Помилка завантаження: {error.message}
          </div>
        )}

        {backtest && (
          <>
            {/* Main Accuracy Card */}
            <div className={`p-6 rounded-lg border-2 ${getAccuracyBg(backtest.accuracy)}`}>
              <div className="text-center">
                <div className="text-sm text-muted-foreground mb-2">Загальна Точність</div>
                <div className={`text-5xl font-bold ${getAccuracyColor(backtest.accuracy)}`}>
                  {backtest.accuracy.toFixed(1)}%
                </div>
                <div className="text-xs text-muted-foreground mt-2">
                  {backtest.correct_predictions} з {backtest.total_predictions} прогнозів
                </div>
              </div>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-2 gap-4">
              {/* High Confidence */}
              <div className="p-4 bg-muted/50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Target className="h-4 w-4 text-primary" />
                  <span className="text-sm font-medium">Висока Довіра (&gt;60%)</span>
                </div>
                <div className="text-2xl font-bold text-profit">
                  {backtest.high_confidence_accuracy.toFixed(1)}%
                </div>
                <div className="text-xs text-muted-foreground">
                  {backtest.high_confidence_predictions} прогнозів
                </div>
              </div>

              {/* Average Confidence */}
              <div className="p-4 bg-muted/50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <TrendingUp className="h-4 w-4 text-primary" />
                  <span className="text-sm font-medium">Середня Довіра</span>
                </div>
                <div className="text-2xl font-bold text-primary">
                  {backtest.average_confidence.toFixed(1)}%
                </div>
                <div className="text-xs text-muted-foreground">
                  Усі прогнози
                </div>
              </div>
            </div>

            {/* Prediction Distribution */}
            <div className="p-4 bg-muted/50 rounded-lg">
              <div className="text-sm font-medium mb-3">Розподіл Прогнозів</div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-profit" />
                    <span className="text-sm">Бичачі</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className="bg-profit text-profit-foreground">
                      {backtest.prediction_distribution.bullish}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      ({((backtest.prediction_distribution.bullish / backtest.total_predictions) * 100).toFixed(0)}%)
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-loss rotate-180" />
                    <span className="text-sm">Ведмежі</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className="bg-loss text-loss-foreground">
                      {backtest.prediction_distribution.bearish}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      ({((backtest.prediction_distribution.bearish / backtest.total_predictions) * 100).toFixed(0)}%)
                    </span>
                  </div>
                </div>

                {backtest.prediction_distribution.neutral > 0 && (
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="h-4 w-4 flex items-center justify-center text-neutral">─</span>
                      <span className="text-sm">Нейтральні</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">
                        {backtest.prediction_distribution.neutral}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        ({((backtest.prediction_distribution.neutral / backtest.total_predictions) * 100).toFixed(0)}%)
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Summary */}
            <div className="flex items-center gap-2 p-3 bg-primary/10 rounded-lg border border-primary/20">
              {backtest.accuracy >= 60 ? (
                <CheckCircle2 className="h-5 w-5 text-profit flex-shrink-0" />
              ) : (
                <XCircle className="h-5 w-5 text-loss flex-shrink-0" />
              )}
              <p className="text-sm text-muted-foreground">
                {backtest.accuracy >= 70 ? (
                  <>Відмінна точність! Модель показує надійні результати.</>
                ) : backtest.accuracy >= 60 ? (
                  <>Хороша точність. Модель працює стабільно.</>
                ) : (
                  <>Точність нижче 60%. Рекомендується перетренування моделі.</>
                )}
              </p>
            </div>
          </>
        )}
        </CardContent>
      )}
    </Card>
  );
};
