import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { 
  Settings as SettingsIcon, 
  Save, 
  RotateCcw, 
  Download, 
  Upload,
  Wifi,
  Bell,
  Shield,
  Palette
} from "lucide-react";
import { userSettings, UserSettings, defaultSettings } from "@/services/userSettings";
import { backend, SyncStatus } from "@/services/backend";
import { signalHistory } from "@/services/signalHistory";

export const Settings = () => {
  const [settings, setSettings] = useState<UserSettings>(userSettings.getSettings());
  const [syncStatus, setSyncStatus] = useState<SyncStatus>(backend.getSyncStatus());
  const [testingConnection, setTestingConnection] = useState(false);
  const [connectionResult, setConnectionResult] = useState<boolean | null>(null);

  useEffect(() => {
    const interval = setInterval(() => {
      setSyncStatus(backend.getSyncStatus());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const handleSettingChange = (key: keyof UserSettings, value: unknown) => {
    const updated = { ...settings, [key]: value };
    setSettings(updated);
    userSettings.updateSettings({ [key]: value });
  };

  const handleReset = () => {
    setSettings(defaultSettings);
    userSettings.resetSettings();
  };

  const handleExportSettings = () => {
    const settingsJson = userSettings.exportSettings();
    const blob = new Blob([settingsJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `forex-settings-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const handleImportSettings = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        if (userSettings.importSettings(content)) {
          setSettings(userSettings.getSettings());
          alert('Налаштування успішно імпортовано');
        } else {
          alert('Помилка імпорту налаштувань');
        }
      };
      reader.readAsText(file);
    }
  };

  const handleTestConnection = async () => {
    setTestingConnection(true);
    try {
      const result = await backend.testConnection();
      setConnectionResult(result);
    } catch {
      setConnectionResult(false);
    } finally {
      setTestingConnection(false);
    }
  };

  const handleClearHistory = () => {
    if (confirm('Ви впевнені, що хочете очистити всю історію сигналів?')) {
      signalHistory.clearHistory();
      alert('Історію очищено');
    }
  };

  return (
    <div className="space-y-6">
      <Card className="bg-gradient-to-br from-card to-card/80 border-border shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <SettingsIcon className="h-5 w-5 text-primary" />
            Налаштування
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="general" className="space-y-6">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="general">Загальні</TabsTrigger>
              <TabsTrigger value="trading">Торгівля</TabsTrigger>
              <TabsTrigger value="notifications">Сповіщення</TabsTrigger>
              <TabsTrigger value="backend">Бекенд</TabsTrigger>
              <TabsTrigger value="data">Дані</TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Palette className="h-4 w-4" />
                    Інтерфейс
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Тема</Label>
                      <Select 
                        value={settings.theme} 
                        onValueChange={(value) => handleSettingChange('theme', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="light">Світла</SelectItem>
                          <SelectItem value="dark">Темна</SelectItem>
                          <SelectItem value="auto">Авто</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Мова</Label>
                      <Select 
                        value={settings.language} 
                        onValueChange={(value) => handleSettingChange('language', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="uk">Українська</SelectItem>
                          <SelectItem value="en">English</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Часовий пояс</Label>
                    <Input
                      value={settings.timezone}
                      onChange={(e) => handleSettingChange('timezone', e.target.value)}
                      placeholder="Europe/Kiev"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Інтервал оновлення (секунди)</Label>
                    <Input
                      type="number"
                      value={settings.updateInterval}
                      onChange={(e) => handleSettingChange('updateInterval', parseInt(e.target.value))}
                      min="5"
                      max="300"
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="trading" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Shield className="h-4 w-4" />
                    Управління Ризиками
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Ризик на угоду (%)</Label>
                      <Input
                        type="number"
                        value={settings.defaultRiskPercent}
                        onChange={(e) => handleSettingChange('defaultRiskPercent', parseFloat(e.target.value))}
                        min="0.1"
                        max="10"
                        step="0.1"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Макс. одночасних позицій</Label>
                      <Input
                        type="number"
                        value={settings.maxSimultaneousPositions}
                        onChange={(e) => handleSettingChange('maxSimultaneousPositions', parseInt(e.target.value))}
                        min="1"
                        max="10"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Макс. щоденний збиток (%)</Label>
                      <Input
                        type="number"
                        value={settings.maxDailyLoss}
                        onChange={(e) => handleSettingChange('maxDailyLoss', parseFloat(e.target.value))}
                        max="0"
                        step="0.1"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label>Макс. щоденний прибуток (%)</Label>
                      <Input
                        type="number"
                        value={settings.maxDailyProfit}
                        onChange={(e) => handleSettingChange('maxDailyProfit', parseFloat(e.target.value))}
                        min="0"
                        step="0.1"
                      />
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={settings.stopTradingOnDailyLimit}
                      onCheckedChange={(checked) => handleSettingChange('stopTradingOnDailyLimit', checked)}
                    />
                    <Label>Зупиняти торгівлю при досягненні денних лімітів</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={settings.autoGenerateSignals}
                      onCheckedChange={(checked) => handleSettingChange('autoGenerateSignals', checked)}
                    />
                    <Label>Автоматично генерувати сигнали</Label>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="notifications" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Bell className="h-4 w-4" />
                    Сповіщення
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={settings.pushNotifications}
                      onCheckedChange={(checked) => handleSettingChange('pushNotifications', checked)}
                    />
                    <Label>Push-сповіщення</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={settings.emailNotifications}
                      onCheckedChange={(checked) => handleSettingChange('emailNotifications', checked)}
                    />
                    <Label>Email-сповіщення</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={settings.signalSoundEnabled}
                      onCheckedChange={(checked) => handleSettingChange('signalSoundEnabled', checked)}
                    />
                    <Label>Звук при новому сигналі</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={settings.highImpactNewsAlerts}
                      onCheckedChange={(checked) => handleSettingChange('highImpactNewsAlerts', checked)}
                    />
                    <Label>Алерти важливих новин</Label>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="backend" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Wifi className="h-4 w-4" />
                    Підключення до сервера
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>API URL</Label>
                    <Input
                      value={settings.apiEndpoint || ''}
                      onChange={(e) => handleSettingChange('apiEndpoint', e.target.value)}
                      placeholder="https://api.example.com"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>API Key</Label>
                    <Input
                      type="password"
                      value={settings.apiKey || ''}
                      onChange={(e) => handleSettingChange('apiKey', e.target.value)}
                      placeholder="Ваш API ключ"
                    />
                  </div>

                  <div className="flex gap-2">
                    <Button
                      onClick={handleTestConnection}
                      disabled={testingConnection}
                      className="flex items-center gap-2"
                    >
                      <Wifi className="h-4 w-4" />
                      {testingConnection ? 'Тестування...' : 'Тест з\'єднання'}
                    </Button>

                    {connectionResult !== null && (
                      <Badge className={connectionResult ? "bg-profit text-profit-foreground" : "bg-loss text-loss-foreground"}>
                        {connectionResult ? 'З\'ЄДНАННЯ ОК' : 'ПОМИЛКА'}
                      </Badge>
                    )}
                  </div>

                  <div className="pt-4 border-t">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium">Статус синхронізації</span>
                      <Badge className={syncStatus.isOnline ? "bg-profit text-profit-foreground" : "bg-loss text-loss-foreground"}>
                        {syncStatus.isOnline ? 'ОНЛАЙН' : 'ОФЛАЙН'}
                      </Badge>
                    </div>
                    <div className="text-xs text-muted-foreground space-y-1">
                      <p>Очікує синхронізації: {syncStatus.pendingChanges}</p>
                      <p>Останння синхронізація: {new Date(syncStatus.lastSync).toLocaleString('uk-UA')}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="data" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Управління даними</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2 mb-4">
                    <Switch
                      checked={settings.showPerformanceMetrics}
                      onCheckedChange={(checked) => handleSettingChange('showPerformanceMetrics', checked)}
                    />
                    <Label>Показувати метрики продуктивності</Label>
                  </div>

                  <div className="flex items-center space-x-2 mb-4">
                    <Switch
                      checked={settings.enableDataExport}
                      onCheckedChange={(checked) => handleSettingChange('enableDataExport', checked)}
                    />
                    <Label>Дозволити експорт даних</Label>
                  </div>

                  <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                    <div className="space-y-2">
                      <Button
                        onClick={handleExportSettings}
                        variant="outline"
                        className="w-full flex items-center gap-2"
                      >
                        <Download className="h-4 w-4" />
                        Експорт налаштувань
                      </Button>

                      <input
                        type="file"
                        accept=".json"
                        onChange={handleImportSettings}
                        style={{ display: 'none' }}
                        id="import-settings"
                      />
                      <Button
                        onClick={() => document.getElementById('import-settings')?.click()}
                        variant="outline"
                        className="w-full flex items-center gap-2"
                      >
                        <Upload className="h-4 w-4" />
                        Імпорт налаштувань
                      </Button>
                    </div>

                    <div className="space-y-2">
                      <Button
                        onClick={handleReset}
                        variant="outline"
                        className="w-full flex items-center gap-2"
                      >
                        <RotateCcw className="h-4 w-4" />
                        Скинути налаштування
                      </Button>

                      <Button
                        onClick={handleClearHistory}
                        variant="destructive"
                        className="w-full"
                      >
                        Очистити історію
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};