import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, TrendingDown, Minus, Brain, ChevronDown, ChevronUp } from "lucide-react";
import { ForexPair } from "@/lib/data";
import { MLPrediction } from "@/services/mlForexApi";
import clsx from "clsx";

const getTrendIcon = (trend: string) => {
  switch (trend) {
    case "BULLISH":
      return <TrendingUp className="h-4 w-4 text-profit" />;
    case "BEARISH":
      return <TrendingDown className="h-4 w-4 text-loss" />;
    default:
      return <Minus className="h-4 w-4 text-neutral" />;
  }
};

const getSignalBadge = (signal: string) => {
  return signal === "BUY" ? (
    <Badge className="bg-profit text-profit-foreground">BUY</Badge>
  ) : (
    <Badge className="bg-loss text-loss-foreground">SELL</Badge>
  );
};

const getProbabilityColor = (probability: number) => {
  if (probability >= 75) return "text-profit";
  if (probability >= 65) return "text-primary";
  return "text-neutral";
};

interface SignalTableProps {
  pairs: ForexPair[];
  selectedPair: ForexPair | null;
  onPairSelect: (pair: ForexPair) => void;
  isLoading?: boolean;
  predictions?: MLPrediction[];
}

export const SignalTable = ({ pairs, selectedPair, onPairSelect, isLoading, predictions = [] }: SignalTableProps) => {
  const [isCollapsed, setIsCollapsed] = useState(false);

  const getPrediction = (pairSymbol: string) => {
    const normalizedSymbol = pairSymbol.replace('/', '');
    return predictions.find(p => p.symbol === normalizedSymbol);
  };

  return (
    <Card className="bg-gradient-to-br from-card to-card/80 border-border shadow-lg">
      <CardHeader
        className="cursor-pointer hover:bg-muted/50 transition-colors"
        onClick={() => setIsCollapsed(!isCollapsed)}
      >
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5 text-primary" />
          Таблиця ML Сигналів • {pairs.length} Пар
          {isCollapsed ? (
            <ChevronDown className="h-4 w-4 text-muted-foreground" />
          ) : (
            <ChevronUp className="h-4 w-4 text-muted-foreground" />
          )}
        </CardTitle>
      </CardHeader>
      {!isCollapsed && (
        <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="border-border">
                <TableHead className="font-semibold">Пара</TableHead>
                <TableHead className="font-semibold">Ціна MT5</TableHead>
                <TableHead className="font-semibold">ML Прогноз</TableHead>
                <TableHead className="font-semibold">H4</TableHead>
                <TableHead className="font-semibold">H1</TableHead>
                <TableHead className="font-semibold">Підтримка</TableHead>
                <TableHead className="font-semibold">Опір</TableHead>
                <TableHead className="font-semibold">Сигнал</TableHead>
                <TableHead className="font-semibold">Entry</TableHead>
                <TableHead className="font-semibold">SL</TableHead>
                <TableHead className="font-semibold">TP</TableHead>
                <TableHead className="font-semibold">Confidence</TableHead>
                <TableHead className="font-semibold">Відкладений ордер</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {pairs.map((pair) => {
                const prediction = getPrediction(pair.pair);
                const isJPY = pair.pair.includes('JPY');

                return (
                  <TableRow
                    key={pair.pair}
                    onClick={() => onPairSelect(pair)}
                    className={clsx(
                      "border-border hover:bg-muted/50 transition-colors cursor-pointer",
                      {
                        "bg-muted/50": selectedPair?.pair === pair.pair,
                      }
                    )}
                  >
                    <TableCell className="font-medium text-primary">{pair.pair}</TableCell>
                    <TableCell className="font-mono text-foreground">{pair.price}</TableCell>

                    {/* ML Prediction */}
                    <TableCell>
                      {prediction && prediction.predicted_price != null && prediction.change_percent != null ? (
                        <div className="flex flex-col gap-0.5">
                          <span className={clsx("font-mono text-xs font-semibold", {
                            "text-profit": prediction.change_percent > 0,
                            "text-loss": prediction.change_percent < 0,
                          })}>
                            {prediction.predicted_price.toFixed(isJPY ? 2 : 4)}
                          </span>
                          <span className={clsx("text-[10px]", {
                            "text-profit": prediction.change_percent > 0,
                            "text-loss": prediction.change_percent < 0,
                          })}>
                            {prediction.change_percent > 0 ? '+' : ''}{prediction.change_percent.toFixed(2)}%
                          </span>
                        </div>
                      ) : (
                        <span className="text-xs text-muted-foreground">-</span>
                      )}
                    </TableCell>

                    <TableCell>
                      <div className="flex items-center gap-1">
                        {getTrendIcon(pair.trendH4)}
                        <span className="text-xs">{pair.trendH4}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        {getTrendIcon(pair.trendH1)}
                        <span className="text-xs">{pair.trendH1}</span>
                      </div>
                    </TableCell>
                    <TableCell className="font-mono text-muted-foreground">{pair.support}</TableCell>
                    <TableCell className="font-mono text-muted-foreground">{pair.resistance}</TableCell>
                    <TableCell>{getSignalBadge(pair.signal)}</TableCell>
                    <TableCell className="font-mono text-foreground">{pair.entry}</TableCell>
                    <TableCell className="font-mono text-loss">{pair.sl}</TableCell>
                    <TableCell className="font-mono text-profit">{pair.tp}</TableCell>

                    {/* Confidence from ML */}
                    <TableCell className={`font-semibold ${getProbabilityColor(pair.probability)}`}>
                      {pair.probability}%
                      {prediction && (
                        <Brain className="inline h-3 w-3 ml-1 text-primary" />
                      )}
                    </TableCell>

                    <TableCell className="font-mono text-xs text-muted-foreground">
                      {pair.pendingOrder}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
        </CardContent>
      )}
    </Card>
  );
};