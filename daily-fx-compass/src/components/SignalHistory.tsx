import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  History, 
  Download, 
  TrendingUp, 
  TrendingDown, 
  Clock,
  Target,
  AlertCircle
} from "lucide-react";
import { signalHistory, TradingSignal, SignalPerformance } from "@/services/signalHistory";

const getStatusBadge = (status: TradingSignal['status']) => {
  const variants = {
    'ACTIVE': { className: "bg-primary text-primary-foreground", label: "АКТИВНИЙ" },
    'FILLED': { className: "bg-profit text-profit-foreground", label: "ВИКОНАНО" },
    'CANCELLED': { className: "bg-neutral text-neutral-foreground", label: "СКАСОВАНО" },
    'TP_HIT': { className: "bg-profit text-profit-foreground", label: "TP" },
    'SL_HIT': { className: "bg-loss text-loss-foreground", label: "SL" },
  };

  const variant = variants[status];
  return <Badge className={variant.className}>{variant.label}</Badge>;
};

const getPnLColor = (pnl: number | undefined) => {
  if (!pnl) return "text-muted-foreground";
  return pnl > 0 ? "text-profit" : "text-loss";
};

export const SignalHistory = () => {
  const [signals, setSignals] = useState<TradingSignal[]>([]);
  const [performance, setPerformance] = useState<SignalPerformance | null>(null);
  const [selectedTimeframe, setSelectedTimeframe] = useState<'today' | 'week' | 'month' | 'all'>('today');

  useEffect(() => {
    const load = () => {
      let filteredSignals = signalHistory.getAllSignals();
      
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
      const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

      switch (selectedTimeframe) {
        case 'today':
          filteredSignals = signalHistory.getSignalsByDateRange(today, now);
          break;
        case 'week':
          filteredSignals = signalHistory.getSignalsByDateRange(weekAgo, now);
          break;
        case 'month':
          filteredSignals = signalHistory.getSignalsByDateRange(monthAgo, now);
          break;
        default:
          break;
      }

      setSignals(filteredSignals);
      
      const perf = signalHistory.getPerformanceMetrics();
      setPerformance(perf);
    };
    
    load();
  }, [selectedTimeframe]);


  const handleExport = () => {
    const csvContent = signalHistory.exportSignalsToCSV();
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `forex_signals_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('uk-UA', {
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatPnL = (pnl: number | undefined) => {
    if (pnl === undefined) return "-";
    return `${pnl > 0 ? '+' : ''}${pnl.toFixed(2)}`;
  };

  return (
    <div className="space-y-6">
      {/* Performance Overview */}
      {performance && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4 text-primary" />
                <div>
                  <p className="text-2xl font-bold text-profit">{performance.winRate}%</p>
                  <p className="text-xs text-muted-foreground">Win Rate</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-primary" />
                <div>
                  <p className={`text-2xl font-bold ${getPnLColor(performance.totalPnL)}`}>
                    {formatPnL(performance.totalPnL)}
                  </p>
                  <p className="text-xs text-muted-foreground">Загальний P&L</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-primary" />
                <div>
                  <p className="text-2xl font-bold text-foreground">{performance.avgHoldTime.toFixed(1)}г</p>
                  <p className="text-xs text-muted-foreground">Сер. час утримання</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <History className="h-4 w-4 text-primary" />
                <div>
                  <p className="text-2xl font-bold text-foreground">{performance.totalSignals}</p>
                  <p className="text-xs text-muted-foreground">Всього сигналів</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Signal History Table */}
      <Card className="bg-gradient-to-br from-card to-card/80 border-border shadow-lg">
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="flex items-center gap-2">
              <History className="h-5 w-5 text-primary" />
              Історія Сигналів
            </CardTitle>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleExport}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Експорт
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={selectedTimeframe} onValueChange={(value) => setSelectedTimeframe(value as 'today' | 'week' | 'month' | 'all')}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="today">Сьогодні</TabsTrigger>
              <TabsTrigger value="week">Тиждень</TabsTrigger>
              <TabsTrigger value="month">Місяць</TabsTrigger>
              <TabsTrigger value="all">Все</TabsTrigger>
            </TabsList>
            
            <TabsContent value={selectedTimeframe} className="mt-6">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Час</TableHead>
                      <TableHead>Пара</TableHead>
                      <TableHead>Сигнал</TableHead>
                      <TableHead>Entry</TableHead>
                      <TableHead>SL</TableHead>
                      <TableHead>TP</TableHead>
                      <TableHead>Статус</TableHead>
                      <TableHead>P&L</TableHead>
                      <TableHead>%</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {signals.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={9} className="text-center text-muted-foreground py-8">
                          <div className="flex flex-col items-center gap-2">
                            <AlertCircle className="h-8 w-8" />
                            <p>Сигналів за вибраний період не знайдено</p>
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : (
                      signals.map((signal) => (
                        <TableRow key={signal.id}>
                          <TableCell className="font-mono text-xs">
                            {formatTimestamp(signal.timestamp)}
                          </TableCell>
                          <TableCell className="font-medium">{signal.pair}</TableCell>
                          <TableCell>
                            <Badge className={signal.signal === 'BUY' ? "bg-profit text-profit-foreground" : "bg-loss text-loss-foreground"}>
                              {signal.signal}
                            </Badge>
                          </TableCell>
                          <TableCell className="font-mono">{signal.entry}</TableCell>
                          <TableCell className="font-mono text-loss">{signal.sl}</TableCell>
                          <TableCell className="font-mono text-profit">{signal.tp}</TableCell>
                          <TableCell>{getStatusBadge(signal.status)}</TableCell>
                          <TableCell className={`font-mono ${getPnLColor(signal.pnl)}`}>
                            {formatPnL(signal.pnl)}
                          </TableCell>
                          <TableCell className="text-muted-foreground">{signal.probability}%</TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};