import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Activity,
  Database,
  Cpu,
  Clock,
  Target,
  TrendingUp,
  RefreshCw,
  ChevronDown,
  ChevronUp
} from "lucide-react";
import { useMLSystemStatus, useMLMarketSummary, useMLTrainingStatus } from "@/hooks/useMLForexData";
import { Progress } from "@/components/ui/progress";

export const MLSystemStatus = () => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { data: systemStatus, isLoading: systemLoading, refetch: refetchSystem } = useMLSystemStatus();
  const { data: marketSummary, isLoading: marketLoading } = useMLMarketSummary();
  const { data: trainingStatus, isLoading: trainingLoading } = useMLTrainingStatus();

  const isLoading = systemLoading || marketLoading || trainingLoading;

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-semibold flex items-center gap-2">
            <Activity className="h-4 w-4 animate-pulse" />
            Завантаження статусу ML системи...
          </CardTitle>
        </CardHeader>
      </Card>
    );
  }

  if (!systemStatus) {
    return (
      <Card className="border-loss">
        <CardHeader>
          <CardTitle className="text-sm font-semibold flex items-center gap-2 text-loss">
            <Activity className="h-4 w-4" />
            ML система недоступна
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Button variant="outline" size="sm" onClick={() => refetchSystem()}>
            <RefreshCw className="h-3 w-3 mr-2" />
            Спробувати знову
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader
        className="cursor-pointer hover:bg-muted/50 transition-colors"
        onClick={() => setIsCollapsed(!isCollapsed)}
      >
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-semibold flex items-center gap-2">
            <Activity className="h-4 w-4 text-primary" />
            Статус ML Системи
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                refetchSystem();
              }}
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
            {isCollapsed ? (
              <ChevronDown className="h-4 w-4 text-muted-foreground" />
            ) : (
              <ChevronUp className="h-4 w-4 text-muted-foreground" />
            )}
          </div>
        </div>
      </CardHeader>
      {!isCollapsed && (
        <CardContent className="space-y-4">
          {/* System Status Grid */}
          <div className="grid grid-cols-2 gap-3">
            {/* MT5 Connection */}
            <div className="flex items-center gap-2">
              <div className={`h-2 w-2 rounded-full ${systemStatus.mt5_connected ? 'bg-profit animate-pulse' : 'bg-loss'}`} />
              <span className="text-xs text-muted-foreground">MT5</span>
              <Badge variant={systemStatus.mt5_connected ? "default" : "destructive"} className="ml-auto text-[10px]">
                {systemStatus.mt5_connected ? "OK" : "OFF"}
              </Badge>
            </div>

            {/* Data Collector */}
            <div className="flex items-center gap-2">
              <Database className="h-3 w-3 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">Збір даних</span>
              <Badge variant={systemStatus.data_collector_running ? "default" : "destructive"} className="ml-auto text-[10px]">
                {systemStatus.data_collector_running ? "ON" : "OFF"}
              </Badge>
            </div>

            {/* Auto Trainer */}
            <div className="flex items-center gap-2">
              <Cpu className="h-3 w-3 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">Авто-тренер</span>
              <Badge variant={systemStatus.auto_trainer_running ? "default" : "destructive"} className="ml-auto text-[10px]">
                {systemStatus.auto_trainer_running ? "ON" : "OFF"}
              </Badge>
            </div>

            {/* Server Uptime */}
            <div className="flex items-center gap-2">
              <Clock className="h-3 w-3 text-muted-foreground" />
              <span className="text-xs text-muted-foreground">Uptime</span>
              <span className="ml-auto text-xs font-medium">{systemStatus.server_uptime}</span>
            </div>
          </div>

          {/* Database Records */}
          <div className="flex items-center justify-between p-2 bg-muted/50 rounded-md">
            <div className="flex items-center gap-2">
              <Database className="h-4 w-4 text-primary" />
              <span className="text-xs text-muted-foreground">Записів у БД</span>
            </div>
            <span className="text-sm font-bold">{systemStatus.database_records.toLocaleString()}</span>
          </div>

          {/* Active Models */}
          <div className="flex items-center justify-between p-2 bg-muted/50 rounded-md">
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-profit" />
              <span className="text-xs text-muted-foreground">Активних моделей</span>
            </div>
            <span className="text-sm font-bold text-profit">{systemStatus.active_models}</span>
          </div>

          {/* Market Summary */}
          {marketSummary && (
            <div className="pt-3 border-t border-border/50 space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-xs text-muted-foreground">Загальна точність</span>
                <span className="text-sm font-bold text-profit">
                  {marketSummary.average_accuracy.toFixed(1)}%
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-xs text-muted-foreground">Всього прогнозів</span>
                <span className="text-sm font-medium">{marketSummary.total_predictions}</span>
              </div>
            </div>
          )}

          {/* Training Status */}
          {trainingStatus && trainingStatus.is_training && (
            <div className="pt-3 border-t border-border/50 space-y-2">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-primary animate-pulse" />
                <span className="text-xs font-medium">Навчання моделі...</span>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span className="text-muted-foreground">{trainingStatus.current_symbol || "Підготовка"}</span>
                  <span className="font-medium">{trainingStatus.progress_percent}%</span>
                </div>
                <Progress value={trainingStatus.progress_percent} className="h-1" />
                <p className="text-[10px] text-muted-foreground">
                  Крок {trainingStatus.current_step} з {trainingStatus.total_steps}
                </p>
              </div>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
};
