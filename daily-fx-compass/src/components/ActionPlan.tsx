import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, AlertTriangle, XCircle, Clock, Brain, TrendingUp, ChevronDown, ChevronUp } from "lucide-react";
import { ForexPair } from "@/lib/data";
import { MLPrediction } from "@/services/mlForexApi";

interface ActionPlanProps {
  pairs?: ForexPair[];
  predictions?: MLPrediction[];
}

interface ActionItem {
  type: "enter" | "wait" | "avoid";
  pair: string;
  action: string;
  priority: "high" | "medium" | "low";
  confidence?: number;
  currentPrice?: string;
  targetPrice?: string;
  signal?: "BUY" | "SELL";
}

const generateMLBasedActions = (pairs: ForexPair[], predictions: MLPrediction[]): ActionItem[] => {
  const actions: ActionItem[] = [];

  // Створюємо map прогнозів для швидкого доступу
  const predictionMap = new Map(
    predictions.map(p => [p.symbol, p])
  );

  // Генеруємо дії на основі ML прогнозів
  pairs.forEach(pair => {
    const normalizedSymbol = pair.pair.replace('/', '');
    const prediction = predictionMap.get(normalizedSymbol);

    if (!prediction || !prediction.predicted_price || prediction.confidence < 60) {
      return; // Пропускаємо пари без прогнозів або з низькою точністю
    }

    const isHighConfidence = prediction.confidence >= 80;
    const isMediumConfidence = prediction.confidence >= 70 && prediction.confidence < 80;
    const changePercent = Math.abs(prediction.change_percent);

    // Генеруємо рекомендацію тільки для значних змін (>0.1%)
    if (changePercent > 0.1) {
      const signal = prediction.change_percent > 0 ? "BUY" : "SELL";
      const targetPrice = prediction.predicted_price.toFixed(pair.pair.includes('JPY') ? 2 : 4);
      const stopLoss = signal === "BUY"
        ? (prediction.current_price * 0.995).toFixed(pair.pair.includes('JPY') ? 2 : 4)
        : (prediction.current_price * 1.005).toFixed(pair.pair.includes('JPY') ? 2 : 4);

      actions.push({
        type: "enter",
        pair: pair.pair,
        action: `${pair.pair}: ${signal} на поточній ціні ${pair.price}. ML прогноз: ${targetPrice} (${prediction.change_percent > 0 ? '+' : ''}${prediction.change_percent.toFixed(2)}%). SL: ${stopLoss}, TP: ${targetPrice}`,
        priority: isHighConfidence ? "high" : isMediumConfidence ? "medium" : "low",
        confidence: prediction.confidence,
        currentPrice: pair.price,
        targetPrice: targetPrice,
        signal: signal
      });
    }
  });

  // Сортуємо за confidence (від найвищого)
  actions.sort((a, b) => (b.confidence || 0) - (a.confidence || 0));

  // Додаємо загальні рекомендації по ризик-менеджменту
  const now = new Date();
  const hour = now.getHours();

  // Попередження перед важливими торговими сесіями
  if (hour >= 13 && hour < 14) {
    actions.push({
      type: "wait",
      pair: "Всі USD пари",
      action: "Очікуємо відкриття Американської сесії (14:30). Можлива висока волатильність.",
      priority: "medium"
    });
  }

  if (hour >= 21) {
    actions.push({
      type: "avoid",
      pair: "Всі пари",
      action: "Після 21:00 ліквідність знижується. Рекомендується закрити позиції або зменшити лоти.",
      priority: "medium"
    });
  }

  return actions;
};

const getActionIcon = (type: string) => {
  switch (type) {
    case "enter":
      return <CheckCircle className="h-5 w-5 text-profit" />;
    case "wait":
      return <Clock className="h-5 w-5 text-primary" />;
    case "avoid":
      return <XCircle className="h-5 w-5 text-loss" />;
    default:
      return <AlertTriangle className="h-5 w-5 text-neutral" />;
  }
};

const getActionBadge = (type: string) => {
  switch (type) {
    case "enter":
      return <Badge className="bg-profit text-profit-foreground">ВХІД</Badge>;
    case "wait":
      return <Badge className="bg-primary text-primary-foreground">ЧЕКАТИ</Badge>;
    case "avoid":
      return <Badge className="bg-loss text-loss-foreground">УНИКАТИ</Badge>;
    default:
      return <Badge className="bg-neutral text-neutral-foreground">ІНШЕ</Badge>;
  }
};

const getPriorityBadge = (priority: string) => {
  switch (priority) {
    case "high":
      return <Badge variant="outline" className="border-loss text-loss">Високий</Badge>;
    case "medium":
      return <Badge variant="outline" className="border-primary text-primary">Середній</Badge>;
    default:
      return <Badge variant="outline" className="border-neutral text-neutral">Низький</Badge>;
  }
};

const riskManagementRules = [
  "Максимальний ризик на угоду: 2% від депозиту",
  "Не більше 3-х одночасних позицій",
  "При досягненні +5% прибутку за день - зменшити лоти на 50%",
  "При збитках -3% за день - припинити торгівлю",
  "Обов'язково використовувати Stop Loss на всіх позиціях"
];

export const ActionPlan = ({ pairs = [], predictions = [] }: ActionPlanProps) => {
  const [isCollapsedActions, setIsCollapsedActions] = useState(false);
  const [isCollapsedRisk, setIsCollapsedRisk] = useState(false);
  const [isCollapsedMarket, setIsCollapsedMarket] = useState(false);

  const actionItems = generateMLBasedActions(pairs, predictions);

  return (
    <div className="space-y-6">
      {/* Action Items - Full Width */}
      <Card className="bg-gradient-to-br from-card to-card/80 border-border shadow-lg">
          <CardHeader
            className="cursor-pointer hover:bg-muted/50 transition-colors"
            onClick={() => setIsCollapsedActions(!isCollapsedActions)}
          >
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5 text-primary" />
              ML План Дій • {actionItems.length} Рекомендацій
              {isCollapsedActions ? (
                <ChevronDown className="h-4 w-4 text-muted-foreground" />
              ) : (
                <ChevronUp className="h-4 w-4 text-muted-foreground" />
              )}
            </CardTitle>
            {!isCollapsedActions && (
              <p className="text-xs text-muted-foreground mt-2">
                Автоматично згенеровано на основі ML прогнозів з confidence ≥60%
              </p>
            )}
          </CardHeader>
          {!isCollapsedActions && (
            <CardContent className="space-y-4">
            {actionItems.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Brain className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>ML прогнози завантажуються...</p>
                <p className="text-xs mt-2">Або немає сигналів з достатньою точністю (≥60%)</p>
              </div>
            ) : (
              actionItems.map((item, index) => (
                <Card key={index} className="bg-muted/30 border-border/50">
                  <CardContent className="pt-4">
                    <div className="flex items-start gap-3">
                      <div className="mt-1">
                        {getActionIcon(item.type)}
                      </div>
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center gap-3 flex-wrap">
                          <span className="font-semibold text-primary">{item.pair}</span>
                          {getActionBadge(item.type)}
                          {getPriorityBadge(item.priority)}
                          {item.confidence && (
                            <Badge variant="outline" className="border-primary text-primary">
                              <Brain className="h-3 w-3 mr-1" />
                              {item.confidence.toFixed(1)}%
                            </Badge>
                          )}
                          {item.signal && (
                            <Badge className={item.signal === "BUY" ? "bg-profit text-profit-foreground" : "bg-loss text-loss-foreground"}>
                              {item.signal}
                            </Badge>
                          )}
                        </div>
                        <p className="text-muted-foreground text-sm leading-relaxed">
                          {item.action}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
            </CardContent>
          )}
      </Card>

      {/* Risk Management and Market Status - Two Columns */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="bg-gradient-to-br from-card to-card/80 border-border shadow-lg">
          <CardHeader
            className="cursor-pointer hover:bg-muted/50 transition-colors"
            onClick={() => setIsCollapsedRisk(!isCollapsedRisk)}
          >
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-primary" />
              Управління Ризиками
              {isCollapsedRisk ? (
                <ChevronDown className="h-4 w-4 text-muted-foreground" />
              ) : (
                <ChevronUp className="h-4 w-4 text-muted-foreground" />
              )}
            </CardTitle>
          </CardHeader>
          {!isCollapsedRisk && (
            <CardContent className="space-y-3">
            {riskManagementRules.map((rule, index) => (
              <div key={index} className="flex items-start gap-3 p-3 bg-muted/30 rounded-md">
                <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-sm text-muted-foreground leading-relaxed">{rule}</p>
              </div>
            ))}
            </CardContent>
          )}
        </Card>

        {/* Market Status */}
        <Card className="bg-gradient-to-br from-card to-card/80 border-border shadow-lg">
          <CardHeader
            className="cursor-pointer hover:bg-muted/50 transition-colors"
            onClick={() => setIsCollapsedMarket(!isCollapsedMarket)}
          >
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-primary" />
              Статус Ринку
              {isCollapsedMarket ? (
                <ChevronDown className="h-4 w-4 text-muted-foreground" />
              ) : (
                <ChevronUp className="h-4 w-4 text-muted-foreground" />
              )}
            </CardTitle>
          </CardHeader>
          {!isCollapsedMarket && (
            <CardContent className="space-y-3">
            <div className="flex justify-between items-center p-3 bg-profit/10 border border-profit/20 rounded-md">
              <span className="text-sm font-medium">Європейська сесія</span>
              <Badge className="bg-profit text-profit-foreground">АКТИВНА</Badge>
            </div>
            <div className="flex justify-between items-center p-3 bg-muted/30 rounded-md">
              <span className="text-sm font-medium">Американська сесія</span>
              <Badge className="bg-neutral text-neutral-foreground">ОЧІКУВАННЯ</Badge>
            </div>
            <div className="flex justify-between items-center p-3 bg-muted/30 rounded-md">
              <span className="text-sm font-medium">Азійська сесія</span>
              <Badge className="bg-muted text-muted-foreground">ЗАКРИТА</Badge>
            </div>
            </CardContent>
          )}
        </Card>
      </div>
    </div>
  );
};