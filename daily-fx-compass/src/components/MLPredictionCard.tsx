import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, TrendingDown, Brain, Activity } from "lucide-react";
import { MLPrediction } from "@/services/mlForexApi";

interface MLPredictionCardProps {
  prediction: MLPrediction;
}

export const MLPredictionCard = ({ prediction }: MLPredictionCardProps) => {
  if (!prediction || prediction.current_price == null || prediction.predicted_price == null) {
    return null;
  }

  const isPositive = prediction.change_percent > 0;
  const isHighConfidence = prediction.confidence >= 80;
  const isMediumConfidence = prediction.confidence >= 60 && prediction.confidence < 80;

  const confidenceColor = isHighConfidence
    ? "bg-profit text-profit-foreground"
    : isMediumConfidence
    ? "bg-yellow-500 text-white"
    : "bg-loss text-loss-foreground";

  const changeColor = isPositive ? "text-profit" : "text-loss";
  const TrendIcon = isPositive ? TrendingUp : TrendingDown;

  return (
    <Card className="border-border/50 hover:border-primary/50 transition-colors">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base font-semibold flex items-center gap-2">
            <Brain className="h-4 w-4 text-primary" />
            ML Прогноз
          </CardTitle>
          <Badge className={confidenceColor}>
            {prediction.confidence.toFixed(1)}% впевненості
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Current vs Predicted Price */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-xs text-muted-foreground mb-1">Поточна ціна</p>
            <p className="text-lg font-bold">
              {prediction.current_price.toFixed(prediction.symbol.includes('JPY') ? 2 : 4)}
            </p>
          </div>
          <div>
            <p className="text-xs text-muted-foreground mb-1">Прогноз</p>
            <p className={`text-lg font-bold ${changeColor}`}>
              {prediction.predicted_price.toFixed(prediction.symbol.includes('JPY') ? 2 : 4)}
            </p>
          </div>
        </div>

        {/* Change Percent */}
        <div className="flex items-center justify-between p-2 bg-muted/50 rounded-md">
          <div className="flex items-center gap-2">
            <TrendIcon className={`h-4 w-4 ${changeColor}`} />
            <span className="text-xs text-muted-foreground">Очікувана зміна</span>
          </div>
          <span className={`text-sm font-bold ${changeColor}`}>
            {isPositive ? "+" : ""}{prediction.change_percent.toFixed(2)}%
          </span>
        </div>

        {/* Model Type */}
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center gap-1 text-muted-foreground">
            <Activity className="h-3 w-3" />
            <span>Модель</span>
          </div>
          <span className="font-medium">{prediction.model_type}</span>
        </div>

        {/* Confidence Level Description */}
        <div className="pt-2 border-t border-border/50">
          <p className="text-xs text-muted-foreground">
            {isHighConfidence && "✓ Висока точність прогнозу"}
            {isMediumConfidence && "⚠ Середня точність прогнозу"}
            {!isHighConfidence && !isMediumConfidence && "⚠ Низька точність прогнозу"}
          </p>
        </div>
      </CardContent>
    </Card>
  );
};
