import { TrendingUp } from "lucide-react";

export const Header = () => {
  const currentTime = new Date().toLocaleString('uk-UA', {
    timeZone: 'Europe/Kiev',
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });

  return (
    <header className="border-b border-border bg-gradient-to-r from-card to-muted/50 backdrop-blur-sm">
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="bg-gradient-to-br from-primary to-primary/80 p-3 rounded-lg shadow-lg">
              <TrendingUp className="h-6 w-6 text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
                Професійний Форекс Аналітик
              </h1>
              <p className="text-sm text-muted-foreground">
                Щоденний план торгівлі • 7 Major пар
              </p>
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm text-muted-foreground">Поточний час (CET)</p>
            <p className="text-lg font-semibold text-foreground">{currentTime}</p>
          </div>
        </div>
      </div>
    </header>
  );
};