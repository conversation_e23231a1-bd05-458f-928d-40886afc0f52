export interface UserSettings {
  // Display preferences
  theme: 'light' | 'dark' | 'auto';
  language: 'uk' | 'en';
  timezone: string;
  
  // Trading preferences
  defaultRiskPercent: number;
  maxSimultaneousPositions: number;
  autoGenerateSignals: boolean;
  signalSoundEnabled: boolean;
  
  // Data preferences
  updateInterval: number; // seconds
  showPerformanceMetrics: boolean;
  enableDataExport: boolean;
  
  // Notification preferences
  emailNotifications: boolean;
  pushNotifications: boolean;
  highImpactNewsAlerts: boolean;
  
  // API preferences
  apiEndpoint?: string;
  apiKey?: string;
  
  // Chart preferences
  chartTimeframes: string[];
  defaultTimeframe: '1h' | '4h' | '1d';
  chartIndicators: string[];
  
  // Risk management
  maxDailyLoss: number;
  maxDailyProfit: number;
  stopTradingOnDailyLimit: boolean;
}

export const defaultSettings: UserSettings = {
  theme: 'dark',
  language: 'uk',
  timezone: 'Europe/Kiev',
  
  defaultRiskPercent: 2,
  maxSimultaneousPositions: 3,
  autoGenerateSignals: true,
  signalSoundEnabled: true,
  
  updateInterval: 1,
  showPerformanceMetrics: true,
  enableDataExport: true,
  
  emailNotifications: false,
  pushNotifications: true,
  highImpactNewsAlerts: true,
  
  chartTimeframes: ['1h', '4h', '1d'],
  defaultTimeframe: '4h',
  chartIndicators: ['SMA20', 'Support', 'Resistance'],
  
  maxDailyLoss: -3,
  maxDailyProfit: 5,
  stopTradingOnDailyLimit: true,
};

class UserSettingsService {
  private storageKey = 'forex_user_settings';

  getSettings(): UserSettings {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (stored) {
        const parsed = JSON.parse(stored);
        // Merge with defaults to ensure all properties exist
        return { ...defaultSettings, ...parsed };
      }
    } catch (error) {
      console.error('Failed to load user settings:', error);
    }
    return defaultSettings;
  }

  updateSettings(updates: Partial<UserSettings>): void {
    const current = this.getSettings();
    const updated = { ...current, ...updates };
    
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(updated));
      this.notifySettingsChanged(updated);
    } catch (error) {
      console.error('Failed to save user settings:', error);
    }
  }

  resetSettings(): void {
    localStorage.removeItem(this.storageKey);
    this.notifySettingsChanged(defaultSettings);
  }

  exportSettings(): string {
    const settings = this.getSettings();
    return JSON.stringify(settings, null, 2);
  }

  importSettings(settingsJson: string): boolean {
    try {
      const settings = JSON.parse(settingsJson);
      
      // Validate that imported settings have the correct structure
      const validatedSettings = this.validateSettings(settings);
      
      localStorage.setItem(this.storageKey, JSON.stringify(validatedSettings));
      this.notifySettingsChanged(validatedSettings);
      return true;
    } catch (error) {
      console.error('Failed to import settings:', error);
      return false;
    }
  }

  private validateSettings(settings: unknown): UserSettings {
    // Ensure all required fields exist with proper types
    const validated: UserSettings = { ...defaultSettings };
    
    if (typeof settings === 'object' && settings !== null && !Array.isArray(settings)) {
      Object.keys(defaultSettings).forEach(key => {
        const typedKey = key as keyof UserSettings;
        if (settings[key] !== undefined) {
          const defaultValue = defaultSettings[typedKey];
          const importedValue = (settings as Record<string, unknown>)[key];
          
          // Type checking
          if (typeof defaultValue === typeof importedValue) {
            if (Array.isArray(defaultValue) && Array.isArray(importedValue)) {
              (validated as any)[typedKey] = importedValue;
            } else if (typeof defaultValue !== 'object') {
              (validated as any)[typedKey] = importedValue;
            }
          }
        }
      });
    }
    
    return validated;
  }

  private notifySettingsChanged(settings: UserSettings): void {
    // Dispatch custom event for components to listen to
    window.dispatchEvent(new CustomEvent('userSettingsChanged', { 
      detail: settings 
    }));
  }

  // Computed properties
  getUpdateIntervalMs(): number {
    return this.getSettings().updateInterval * 1000;
  }

  shouldShowPerformance(): boolean {
    return this.getSettings().showPerformanceMetrics;
  }

  getTheme(): 'light' | 'dark' | 'auto' {
    return this.getSettings().theme;
  }

  getRiskManagementSettings() {
    const settings = this.getSettings();
    return {
      defaultRiskPercent: settings.defaultRiskPercent,
      maxSimultaneousPositions: settings.maxSimultaneousPositions,
      maxDailyLoss: settings.maxDailyLoss,
      maxDailyProfit: settings.maxDailyProfit,
      stopTradingOnDailyLimit: settings.stopTradingOnDailyLimit,
    };
  }
}

export const userSettings = new UserSettingsService();