const ML_API_BASE = 'http://84.247.166.52:5000/api';

export interface MLPrice {
  symbol: string;
  bid: number;
  ask: number;
  spread: number;
  time: string;
}

export interface MLPrediction {
  symbol: string;
  timeframe: string;
  direction: 'UP' | 'DOWN';
  confidence: number;
  current_price: number;
  predicted_price: number;
  change_percent: number;
  timestamp: string;
}

export interface MLAllPricesResponse {
  prices: MLPrice[];
  count: number;
  timestamp: string;
}

export interface MLAllPredictionsResponse {
  predictions: MLPrediction[];
  count: number;
  timestamp: string;
}

export interface MLSystemStatus {
  mt5_connected: boolean;
  mt5_available: boolean;
  timestamp: string;
  available_pairs: string[];
  last_mt5_check: string;
}

export interface MLMarketSummary {
  total_pairs: number;
  active_pairs: number;
  total_predictions: number;
  average_accuracy: number;
  data_freshness: string;
}

export interface MLTrainingStatus {
  status: 'idle' | 'training' | 'completed';
  progress?: number;
  symbol?: string;
  timeframe?: string;
  epoch?: number;
  total_epochs?: number;
  current_model?: number;
  total_models?: number;
  last_update?: string;
  model_results?: Record<string, { mae: number; accuracy: number; timestamp: string }>;
}

export interface MLBacktestResult {
  symbol: string;
  total_predictions: number;
  correct_predictions: number;
  accuracy: number;
  high_confidence_predictions: number;
  high_confidence_accuracy: number;
  average_confidence: number;
  prediction_distribution: {
    bullish: number;
    bearish: number;
    neutral: number;
  };
}

export interface MLHistoricalData {
  symbol: string;
  data: Array<{
    timestamp: string;
    price: number;
    predicted_price?: number;
    confidence?: number;
    actual_change?: number;
    predicted_change?: number;
  }>;
  count: number;
}

class MLForexApiService {
  private async fetchWithTimeout(url: string, timeout: number = 5000): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, { signal: controller.signal });
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  async getAllPrices(): Promise<MLAllPricesResponse> {
    try {
      // Get status first to get available pairs
      const statusResponse = await this.fetchWithTimeout(`${ML_API_BASE}/status`);
      if (!statusResponse.ok) throw new Error(`HTTP ${statusResponse.status}`);
      const status: MLSystemStatus = await statusResponse.json();

      // Fetch quotes for all available pairs
      const pricePromises = status.available_pairs.map(async (symbol) => {
        try {
          const response = await this.fetchWithTimeout(`${ML_API_BASE}/quote/${symbol}`);
          if (!response.ok) return null;
          const data = await response.json();
          if (data.status === 'ok') {
            return {
              symbol: data.symbol,
              bid: data.bid,
              ask: data.ask,
              spread: data.spread,
              time: data.time,
            };
          }
          return null;
        } catch {
          return null;
        }
      });

      const prices = (await Promise.all(pricePromises)).filter((p): p is MLPrice => p !== null);

      return {
        prices,
        count: prices.length,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Failed to fetch ML prices:', error);
      throw new Error('ML API недоступний. Перевірте з\'єднання.');
    }
  }

  async getPrice(symbol: string): Promise<MLPrice> {
    try {
      const response = await this.fetchWithTimeout(`${ML_API_BASE}/quote/${symbol}`);
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      const data = await response.json();
      if (data.status === 'ok') {
        return {
          symbol: data.symbol,
          bid: data.bid,
          ask: data.ask,
          spread: data.spread,
          time: data.time,
        };
      }
      throw new Error('Invalid response');
    } catch (error) {
      console.error(`Failed to fetch price for ${symbol}:`, error);
      throw error;
    }
  }

  async getAllPredictions(timeframe: string = 'H1'): Promise<MLAllPredictionsResponse> {
    try {
      const response = await this.fetchWithTimeout(
        `${ML_API_BASE}/predict/summary?timeframe=${timeframe}`
      );
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      const data = await response.json();

      if (data.status === 'ok') {
        const predictions: MLPrediction[] = data.results.map((pred: any) => ({
          symbol: pred.symbol,
          timeframe: pred.timeframe,
          direction: pred.direction,
          confidence: pred.confidence,
          current_price: pred.current_price,
          predicted_price: pred.predicted_price,
          change_percent:
            ((pred.predicted_price - pred.current_price) / pred.current_price) * 100,
          timestamp: pred.timestamp,
        }));

        return {
          predictions,
          count: predictions.length,
          timestamp: new Date().toISOString(),
        };
      }
      throw new Error('Invalid response');
    } catch (error) {
      console.error('Failed to fetch ML predictions:', error);
      throw new Error('ML прогнози недоступні');
    }
  }

  async getPrediction(symbol: string, timeframe: string = 'H1'): Promise<MLPrediction> {
    try {
      const response = await this.fetchWithTimeout(
        `${ML_API_BASE}/predict?symbol=${symbol}&timeframe=${timeframe}`
      );
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      const data = await response.json();

      if (data.status === 'ok') {
        return {
          symbol: data.symbol,
          timeframe: data.timeframe,
          direction: data.direction,
          confidence: data.confidence,
          current_price: data.current_price,
          predicted_price: data.predicted_price,
          change_percent:
            ((data.predicted_price - data.current_price) / data.current_price) * 100,
          timestamp: data.timestamp,
        };
      }
      throw new Error('Invalid response');
    } catch (error) {
      console.error(`Failed to predict ${symbol}:`, error);
      throw error;
    }
  }

  async getSystemStatus(): Promise<MLSystemStatus> {
    try {
      const response = await this.fetchWithTimeout(`${ML_API_BASE}/status`);
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch system status:', error);
      throw error;
    }
  }

  async getMarketSummary(): Promise<MLMarketSummary> {
    try {
      // Get status and predictions to calculate summary
      const [status, predictions] = await Promise.all([
        this.getSystemStatus(),
        this.getAllPredictions(),
      ]);

      const averageAccuracy =
        predictions.predictions.reduce((sum, p) => sum + p.confidence, 0) /
        predictions.predictions.length;

      return {
        total_pairs: status.available_pairs.length,
        active_pairs: status.available_pairs.length,
        total_predictions: predictions.count,
        average_accuracy: averageAccuracy,
        data_freshness: status.last_mt5_check,
      };
    } catch (error) {
      console.error('Failed to fetch market summary:', error);
      throw error;
    }
  }

  async getTrainingStatus(): Promise<MLTrainingStatus> {
    try {
      const response = await this.fetchWithTimeout(`${ML_API_BASE}/training_progress`);
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch training status:', error);
      throw error;
    }
  }

  async retrainModel(symbol: string): Promise<boolean> {
    // This endpoint is not available in new API
    console.warn('Retrain model endpoint not available in new API');
    return false;
  }

  async forceUpdate(): Promise<boolean> {
    // This endpoint is not available in new API
    console.warn('Force update endpoint not available in new API');
    return false;
  }

  async getBacktest(symbol: string, days: number = 30): Promise<MLBacktestResult> {
    try {
      // Use signals history as backtest data
      const response = await this.fetchWithTimeout(
        `${ML_API_BASE}/signals?symbol=${symbol}&limit=${days * 24}`,
        30000
      );
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      const data = await response.json();

      if (data.status === 'ok' && data.data.length > 0) {
        // Calculate backtest metrics from signals
        const signals = data.data;
        const totalPredictions = signals.length;

        // Count correct predictions (simplified - need actual outcome data)
        const correctPredictions = signals.filter((s: any) => {
          const predicted = s.predicted_price > s.current_price ? 'UP' : 'DOWN';
          return predicted === s.trend_direction;
        }).length;

        const accuracy = (correctPredictions / totalPredictions) * 100;

        const highConfidenceSignals = signals.filter((s: any) => s.confidence >= 60);
        const highConfidenceCorrect = highConfidenceSignals.filter((s: any) => {
          const predicted = s.predicted_price > s.current_price ? 'UP' : 'DOWN';
          return predicted === s.trend_direction;
        }).length;

        const avgConfidence =
          signals.reduce((sum: number, s: any) => sum + s.confidence, 0) / signals.length;

        const distribution = signals.reduce(
          (acc: any, s: any) => {
            if (s.trend_direction === 'UP') acc.bullish++;
            else if (s.trend_direction === 'DOWN') acc.bearish++;
            else acc.neutral++;
            return acc;
          },
          { bullish: 0, bearish: 0, neutral: 0 }
        );

        return {
          symbol,
          total_predictions: totalPredictions,
          correct_predictions: correctPredictions,
          accuracy,
          high_confidence_predictions: highConfidenceSignals.length,
          high_confidence_accuracy:
            highConfidenceSignals.length > 0
              ? (highConfidenceCorrect / highConfidenceSignals.length) * 100
              : 0,
          average_confidence: avgConfidence,
          prediction_distribution: distribution,
        };
      }

      // Return default if no data
      return {
        symbol,
        total_predictions: 0,
        correct_predictions: 0,
        accuracy: 0,
        high_confidence_predictions: 0,
        high_confidence_accuracy: 0,
        average_confidence: 0,
        prediction_distribution: { bullish: 0, bearish: 0, neutral: 0 },
      };
    } catch (error) {
      console.error(`Failed to fetch backtest for ${symbol}:`, error);
      throw error;
    }
  }

  async getHistoricalData(symbol: string, limit: number = 100): Promise<MLHistoricalData> {
    try {
      const response = await this.fetchWithTimeout(
        `${ML_API_BASE}/history?symbol=${symbol}&timeframe=H1&count=${limit}`,
        30000
      );
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      const apiData = await response.json();

      if (apiData.status === 'ok') {
        const data = apiData.data.map((item: any) => ({
          timestamp: item.time,
          price: item.close,
        }));

        return {
          symbol,
          data,
          count: data.length,
        };
      }
      throw new Error('Invalid response');
    } catch (error) {
      console.error(`Failed to fetch historical data for ${symbol}:`, error);
      throw error;
    }
  }

  async getPredictionsHistory(symbol: string, limit: number = 100): Promise<MLHistoricalData> {
    try {
      const response = await this.fetchWithTimeout(
        `${ML_API_BASE}/signals?symbol=${symbol}&limit=${limit}`,
        30000
      );
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      const apiData = await response.json();

      if (apiData.status === 'ok') {
        const data = apiData.data.map((item: any) => ({
          timestamp: item.timestamp,
          price: item.current_price,
          predicted_price: item.predicted_price,
          confidence: item.confidence,
        }));

        return {
          symbol,
          data,
          count: data.length,
        };
      }
      throw new Error('Invalid response');
    } catch (error) {
      console.error(`Failed to fetch predictions history for ${symbol}:`, error);
      throw error;
    }
  }

  // Helper to convert symbol format: EUR/USD -> EURUSD
  normalizeSymbol(symbol: string): string {
    return symbol.replace('/', '');
  }

  // Helper to format symbol: EURUSD -> EUR/USD
  formatSymbol(symbol: string): string {
    if (symbol.includes('/')) return symbol;
    if (symbol.length === 6) {
      return `${symbol.slice(0, 3)}/${symbol.slice(3)}`;
    }
    return symbol;
  }
}

export const mlForexApi = new MLForexApiService();
