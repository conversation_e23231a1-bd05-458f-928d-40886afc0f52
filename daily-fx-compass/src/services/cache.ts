interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number; // time to live in milliseconds
}

class CacheService {
  private cache = new Map<string, CacheItem<unknown>>();
  private maxSize = 100; // Maximum number of cached items

  set<T>(key: string, data: T, ttlMs: number = 300000): void { // 5 minutes default
    // Remove oldest items if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = Array.from(this.cache.keys())[0];
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlMs
    });
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    // Check if item has expired
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data as T;
  }

  has(key: string): boolean {
    const item = this.cache.get(key);
    
    if (!item) {
      return false;
    }

    // Check if item has expired
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  // Clean up expired items
  cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    this.cache.forEach((item, key) => {
      if (now - item.timestamp > item.ttl) {
        keysToDelete.push(key);
      }
    });

    keysToDelete.forEach(key => this.cache.delete(key));
  }

  // Get cache statistics
  getStats() {
    const now = Date.now();
    let validItems = 0;
    let expiredItems = 0;

    this.cache.forEach((item) => {
      if (now - item.timestamp > item.ttl) {
        expiredItems++;
      } else {
        validItems++;
      }
    });

    return {
      totalItems: this.cache.size,
      validItems,
      expiredItems,
      hitRate: validItems / Math.max(this.cache.size, 1),
      memoryUsage: this.estimateMemoryUsage()
    };
  }

  private estimateMemoryUsage(): number {
    // Rough estimation in bytes
    let size = 0;
    this.cache.forEach((item, key) => {
      size += key.length * 2; // approximate size of string key
      size += JSON.stringify(item).length * 2; // approximate size of data
    });
    return size;
  }

  // Specific caching methods for forex data
  cacheForexRates(rates: unknown, ttl: number = 30000): void { // 30 seconds for rates
    this.set('forex_rates', rates, ttl);
  }

  getCachedForexRates(): unknown | null {
    return this.get('forex_rates');
  }

  cacheHistoricalData(pair: string, period: string, data: unknown, ttl: number = 300000): void { // 5 minutes for historical
    const key = `historical_${pair}_${period}`;
    this.set(key, data, ttl);
  }

  getCachedHistoricalData(pair: string, period: string): unknown | null {
    const key = `historical_${pair}_${period}`;
    return this.get(key);
  }

  cacheAnalysis(pair: string, analysis: unknown, ttl: number = 600000): void { // 10 minutes for analysis
    const key = `analysis_${pair}`;
    this.set(key, analysis, ttl);
  }

  getCachedAnalysis(pair: string): unknown | null {
    const key = `analysis_${pair}`;
    return this.get(key);
  }

  // Start automatic cleanup interval
  startCleanupInterval(intervalMs: number = 300000): void { // 5 minutes
    setInterval(() => {
      this.cleanup();
    }, intervalMs);
  }
}

export const cache = new CacheService();

// Start automatic cleanup
cache.startCleanupInterval();