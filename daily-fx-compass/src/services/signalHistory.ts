export interface TradingSignal {
  id: string;
  pair: string;
  signal: 'BUY' | 'SELL';
  entry: number;
  sl: number;
  tp: number;
  probability: number;
  timestamp: number;
  status: 'ACTIVE' | 'FILLED' | 'CANCELLED' | 'TP_HIT' | 'SL_HIT';
  fillPrice?: number;
  exitPrice?: number;
  exitTimestamp?: number;
  pnl?: number;
  notes?: string;
}

export interface SignalPerformance {
  totalSignals: number;
  winRate: number;
  avgPnL: number;
  totalPnL: number;
  bestTrade: number;
  worstTrade: number;
  avgHoldTime: number;
}

class SignalHistoryService {
  private storageKey = 'forex_signal_history';
  private performanceKey = 'forex_performance';

  saveSignal(signal: TradingSignal): void {
    const signals = this.getAllSignals();
    signals.unshift(signal); // Add to beginning
    
    // Keep only last 1000 signals
    if (signals.length > 1000) {
      signals.splice(1000);
    }
    
    localStorage.setItem(this.storageKey, JSON.stringify(signals));
    this.updatePerformanceMetrics(signals);
  }

  getAllSignals(): TradingSignal[] {
    try {
      const data = localStorage.getItem(this.storageKey);
      return data ? JSON.parse(data) : [];
    } catch {
      return [];
    }
  }

  getSignalsByPair(pair: string): TradingSignal[] {
    return this.getAllSignals().filter(signal => signal.pair === pair);
  }

  getSignalsByDateRange(startDate: Date, endDate: Date): TradingSignal[] {
    const start = startDate.getTime();
    const end = endDate.getTime();
    
    return this.getAllSignals().filter(signal => 
      signal.timestamp >= start && signal.timestamp <= end
    );
  }

  updateSignalStatus(signalId: string, updates: Partial<TradingSignal>): void {
    const signals = this.getAllSignals();
    const index = signals.findIndex(s => s.id === signalId);
    
    if (index !== -1) {
      signals[index] = { ...signals[index], ...updates };
      localStorage.setItem(this.storageKey, JSON.stringify(signals));
      this.updatePerformanceMetrics(signals);
    }
  }

  private updatePerformanceMetrics(signals: TradingSignal[]): void {
    const closedSignals = signals.filter(s => 
      s.status === 'TP_HIT' || s.status === 'SL_HIT'
    );

    if (closedSignals.length === 0) {
      return;
    }

    const winningTrades = closedSignals.filter(s => (s.pnl || 0) > 0);
    const winRate = (winningTrades.length / closedSignals.length) * 100;
    
    const totalPnL = closedSignals.reduce((sum, s) => sum + (s.pnl || 0), 0);
    const avgPnL = totalPnL / closedSignals.length;
    
    const pnlValues = closedSignals.map(s => s.pnl || 0);
    const bestTrade = Math.max(...pnlValues);
    const worstTrade = Math.min(...pnlValues);
    
    const holdTimes = closedSignals
      .filter(s => s.exitTimestamp)
      .map(s => (s.exitTimestamp! - s.timestamp) / (1000 * 60 * 60)); // hours
    
    const avgHoldTime = holdTimes.length > 0 
      ? holdTimes.reduce((sum, time) => sum + time, 0) / holdTimes.length 
      : 0;

    const performance: SignalPerformance = {
      totalSignals: closedSignals.length,
      winRate: Number(winRate.toFixed(2)),
      avgPnL: Number(avgPnL.toFixed(2)),
      totalPnL: Number(totalPnL.toFixed(2)),
      bestTrade: Number(bestTrade.toFixed(2)),
      worstTrade: Number(worstTrade.toFixed(2)),
      avgHoldTime: Number(avgHoldTime.toFixed(2))
    };

    localStorage.setItem(this.performanceKey, JSON.stringify(performance));
  }

  getPerformanceMetrics(): SignalPerformance | null {
    try {
      const data = localStorage.getItem(this.performanceKey);
      return data ? JSON.parse(data) : null;
    } catch {
      return null;
    }
  }

  generateSignalFromPair(pair: { pair: string; signal: 'BUY' | 'SELL'; entry: string; sl: string; tp: string; probability: number; trendH4: string; trendH1: string }): TradingSignal {
    return {
      id: `signal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      pair: pair.pair,
      signal: pair.signal,
      entry: parseFloat(pair.entry),
      sl: parseFloat(pair.sl),
      tp: parseFloat(pair.tp),
      probability: pair.probability,
      timestamp: Date.now(),
      status: 'ACTIVE',
      notes: `Auto-generated signal based on ${pair.trendH4}/${pair.trendH1} trends`
    };
  }

  exportSignalsToCSV(): string {
    const signals = this.getAllSignals();
    const headers = [
      'ID', 'Pair', 'Signal', 'Entry', 'SL', 'TP', 'Probability', 
      'Status', 'Fill Price', 'Exit Price', 'PnL', 'Timestamp', 'Exit Time', 'Notes'
    ];
    
    const csvContent = [
      headers.join(','),
      ...signals.map(signal => [
        signal.id,
        signal.pair,
        signal.signal,
        signal.entry,
        signal.sl,
        signal.tp,
        signal.probability,
        signal.status,
        signal.fillPrice || '',
        signal.exitPrice || '',
        signal.pnl || '',
        new Date(signal.timestamp).toISOString(),
        signal.exitTimestamp ? new Date(signal.exitTimestamp).toISOString() : '',
        `"${(signal.notes || '').replace(/"/g, '""')}"`
      ].join(','))
    ].join('\n');
    
    return csvContent;
  }

  clearHistory(): void {
    localStorage.removeItem(this.storageKey);
    localStorage.removeItem(this.performanceKey);
  }
}

export const signalHistory = new SignalHistoryService();