import { TradingSignal, SignalPerformance } from './signalHistory';
import { UserSettings } from './userSettings';

export interface BackendConfig {
  apiUrl: string;
  apiKey?: string;
  userId?: string;
}

export interface SyncStatus {
  lastSync: number;
  pendingChanges: number;
  isOnline: boolean;
  syncInProgress: boolean;
}

class BackendService {
  private config: BackendConfig | null = null;
  private syncQueue: Array<{ type: string; data: unknown; timestamp: number }> = [];
  private isOnline = navigator.onLine;
  private syncInProgress = false;

  constructor() {
    // Monitor online status
    window.addEventListener('online', () => {
      this.isOnline = true;
      this.processSyncQueue();
    });
    
    window.addEventListener('offline', () => {
      this.isOnline = false;
    });
  }

  configure(config: BackendConfig): void {
    this.config = config;
  }

  isConfigured(): boolean {
    return this.config !== null && this.config.apiUrl.length > 0;
  }

  // Signals management
  async saveSignal(signal: TradingSignal): Promise<boolean> {
    if (!this.isConfigured()) {
      return this.queueForSync('saveSignal', signal);
    }

    try {
      const response = await fetch(`${this.config!.apiUrl}/signals`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config!.apiKey && { 'Authorization': `Bearer ${this.config!.apiKey}` })
        },
        body: JSON.stringify(signal)
      });

      return response.ok;
    } catch (error) {
      console.error('Failed to save signal to backend:', error);
      return this.queueForSync('saveSignal', signal);
    }
  }

  async updateSignal(signalId: string, updates: Partial<TradingSignal>): Promise<boolean> {
    if (!this.isConfigured()) {
      return this.queueForSync('updateSignal', { signalId, updates });
    }

    try {
      const response = await fetch(`${this.config!.apiUrl}/signals/${signalId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config!.apiKey && { 'Authorization': `Bearer ${this.config!.apiKey}` })
        },
        body: JSON.stringify(updates)
      });

      return response.ok;
    } catch (error) {
      console.error('Failed to update signal:', error);
      return this.queueForSync('updateSignal', { signalId, updates });
    }
  }

  async getSignals(limit: number = 100): Promise<TradingSignal[]> {
    if (!this.isConfigured()) {
      return [];
    }

    try {
      const response = await fetch(`${this.config!.apiUrl}/signals?limit=${limit}`, {
        headers: {
          ...(this.config!.apiKey && { 'Authorization': `Bearer ${this.config!.apiKey}` })
        }
      });

      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      console.error('Failed to fetch signals:', error);
    }

    return [];
  }

  // Settings management
  async saveSettings(settings: UserSettings): Promise<boolean> {
    if (!this.isConfigured()) {
      return this.queueForSync('saveSettings', settings);
    }

    try {
      const response = await fetch(`${this.config!.apiUrl}/settings`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config!.apiKey && { 'Authorization': `Bearer ${this.config!.apiKey}` })
        },
        body: JSON.stringify(settings)
      });

      return response.ok;
    } catch (error) {
      console.error('Failed to save settings:', error);
      return this.queueForSync('saveSettings', settings);
    }
  }

  async getSettings(): Promise<UserSettings | null> {
    if (!this.isConfigured()) {
      return null;
    }

    try {
      const response = await fetch(`${this.config!.apiUrl}/settings`, {
        headers: {
          ...(this.config!.apiKey && { 'Authorization': `Bearer ${this.config!.apiKey}` })
        }
      });

      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      console.error('Failed to fetch settings:', error);
    }

    return null;
  }

  // Performance data
  async savePerformance(performance: SignalPerformance): Promise<boolean> {
    if (!this.isConfigured()) {
      return this.queueForSync('savePerformance', performance);
    }

    try {
      const response = await fetch(`${this.config!.apiUrl}/performance`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config!.apiKey && { 'Authorization': `Bearer ${this.config!.apiKey}` })
        },
        body: JSON.stringify(performance)
      });

      return response.ok;
    } catch (error) {
      console.error('Failed to save performance data:', error);
      return this.queueForSync('savePerformance', performance);
    }
  }

  // Sync management
  private queueForSync(type: string, data: unknown): boolean {
    this.syncQueue.push({
      type,
      data,
      timestamp: Date.now()
    });

    // Try to sync immediately if online
    if (this.isOnline) {
      this.processSyncQueue();
    }

    return true; // Queued successfully
  }

  private async processSyncQueue(): Promise<void> {
    if (!this.isConfigured() || !this.isOnline || this.syncInProgress || this.syncQueue.length === 0) {
      return;
    }

    this.syncInProgress = true;

    try {
      const batch = this.syncQueue.splice(0, 10); // Process in batches
      
      for (const item of batch) {
        try {
          switch (item.type) {
            case 'saveSignal':
              if (this.isValidSignal(item.data)) {
                await this.saveSignal(item.data as TradingSignal);
              }
              break;
            case 'updateSignal':
              if (this.isValidUpdateData(item.data)) {
                const updateData = item.data as { signalId: string; updates: Partial<TradingSignal> };
                await this.updateSignal(updateData.signalId, updateData.updates);
              }
              break;
            case 'saveSettings':
              if (this.isValidSettings(item.data)) {
                await this.saveSettings(item.data as UserSettings);
              }
              break;
            case 'savePerformance':
              if (this.isValidPerformance(item.data)) {
                await this.savePerformance(item.data as SignalPerformance);
              }
              break;
          }
        } catch (error) {
          console.error(`Failed to sync ${item.type}:`, error);
          // Re-queue failed items
          this.syncQueue.push(item);
        }
      }

      // Continue processing if there are more items
      if (this.syncQueue.length > 0) {
        setTimeout(() => this.processSyncQueue(), 1000);
      }
    } finally {
      this.syncInProgress = false;
    }
  }

  getSyncStatus(): SyncStatus {
    return {
      lastSync: Date.now(), // Should be tracked properly
      pendingChanges: this.syncQueue.length,
      isOnline: this.isOnline,
      syncInProgress: this.syncInProgress
    };
  }

  // Utility methods
  async testConnection(): Promise<boolean> {
    if (!this.isConfigured()) {
      return false;
    }

    try {
      const response = await fetch(`${this.config!.apiUrl}/health`, {
        method: 'GET',
        headers: {
          ...(this.config!.apiKey && { 'Authorization': `Bearer ${this.config!.apiKey}` })
        }
      });

      return response.ok;
    } catch {
      return false;
    }
  }

  clearSyncQueue(): void {
    this.syncQueue = [];
  }

  // Type validation helpers
  private isValidSignal(data: unknown): boolean {
    return typeof data === 'object' && data !== null && 
           'id' in data && 'pair' in data && 'signal' in data;
  }

  private isValidUpdateData(data: unknown): boolean {
    return typeof data === 'object' && data !== null && 
           'signalId' in data && 'updates' in data;
  }

  private isValidSettings(data: unknown): boolean {
    return typeof data === 'object' && data !== null && 
           'theme' in data && 'language' in data;
  }

  private isValidPerformance(data: unknown): boolean {
    return typeof data === 'object' && data !== null && 
           'totalSignals' in data && 'winRate' in data;
  }
}

export const backend = new BackendService();