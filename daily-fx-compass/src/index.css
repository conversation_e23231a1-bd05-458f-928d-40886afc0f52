@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 5.9% 10%;
    --popover-foreground: 0 0% 98%;

    --primary: 47 96% 53%;
    --primary-foreground: 240 5.9% 10%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 47 96% 53%;

    /* Financial color scheme */
    --profit: 120 100% 40%;
    --profit-foreground: 0 0% 98%;
    --loss: 0 84% 60%;
    --loss-foreground: 0 0% 98%;
    --neutral: 240 5% 64.9%;
    --neutral-foreground: 0 0% 98%;
    
    /* Professional gradients */
    --gradient-primary: linear-gradient(135deg, hsl(47 96% 53%), hsl(45 100% 60%));
    --gradient-card: linear-gradient(145deg, hsl(240 5.9% 10%), hsl(240 3.7% 15.9%));
    --gradient-profit: linear-gradient(135deg, hsl(120 100% 40%), hsl(120 100% 50%));
    --gradient-loss: linear-gradient(135deg, hsl(0 84% 60%), hsl(0 100% 70%));
    
    /* Shadows */
    --shadow-elegant: 0 10px 30px -10px hsl(240 10% 3.9% / 0.3);
    --shadow-glow: 0 0 40px hsl(47 96% 53% / 0.15);
    
    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Keep same dark theme as root since we're using financial dark theme */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
