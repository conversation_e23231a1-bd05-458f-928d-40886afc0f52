export type ForexPair = {
  pair: string;
  price: string;
  trendH4: "BULLISH" | "BEARISH" | "NEUTRAL";
  trendH1: "BULLISH" | "BEARISH" | "NEUTRAL";
  support: string;
  resistance: string;
  signal: "BUY" | "SELL";
  entry: string;
  sl: string;
  tp: string;
  probability: number;
  pendingOrder: string;
};

export const forexPairs: ForexPair[] = [
  {
    pair: "EUR/USD",
    price: "1.0876",
    trendH4: "BULLISH",
    trendH1: "NEUTRAL",
    support: "1.0850",
    resistance: "1.0920",
    signal: "BUY",
    entry: "1.0880",
    sl: "1.0840",
    tp: "1.0940",
    probability: 75,
    pendingOrder: "BUY LIMIT 1.0865"
  },
  {
    pair: "GBP/USD",
    price: "1.2654",
    trendH4: "BEARISH",
    trendH1: "BEARISH",
    support: "1.2600",
    resistance: "1.2700",
    signal: "SELL",
    entry: "1.2650",
    sl: "1.2690",
    tp: "1.2580",
    probability: 80,
    pendingOrder: "SELL STOP 1.2635"
  },
  {
    pair: "USD/JPY",
    price: "149.85",
    trendH4: "BULLISH",
    trendH1: "BULLISH",
    support: "149.20",
    resistance: "150.50",
    signal: "BUY",
    entry: "149.90",
    sl: "149.00",
    tp: "151.00",
    probability: 70,
    pendingOrder: "BUY STOP 150.10"
  },
  {
    pair: "USD/CHF",
    price: "0.8745",
    trendH4: "NEUTRAL",
    trendH1: "BEARISH",
    support: "0.8720",
    resistance: "0.8780",
    signal: "SELL",
    entry: "0.8750",
    sl: "0.8790",
    tp: "0.8700",
    probability: 65,
    pendingOrder: "SELL LIMIT 0.8765"
  },
  {
    pair: "AUD/USD",
    price: "0.6587",
    trendH4: "BEARISH",
    trendH1: "NEUTRAL",
    support: "0.6550",
    resistance: "0.6620",
    signal: "SELL",
    entry: "0.6585",
    sl: "0.6620",
    tp: "0.6540",
    probability: 68,
    pendingOrder: "SELL STOP 0.6575"
  },
  {
    pair: "NZD/USD",
    price: "0.6123",
    trendH4: "BEARISH",
    trendH1: "BEARISH",
    support: "0.6100",
    resistance: "0.6150",
    signal: "SELL",
    entry: "0.6120",
    sl: "0.6155",
    tp: "0.6080",
    probability: 72,
    pendingOrder: "SELL LIMIT 0.6135"
  },
  {
    pair: "USD/CAD",
    price: "1.3567",
    trendH4: "BULLISH",
    trendH1: "NEUTRAL",
    support: "1.3540",
    resistance: "1.3600",
    signal: "BUY",
    entry: "1.3570",
    sl: "1.3530",
    tp: "1.3620",
    probability: 63,
    pendingOrder: "BUY LIMIT 1.3555"
  },
  {
    pair: "EUR/GBP",
    price: "0.8590",
    trendH4: "NEUTRAL",
    trendH1: "BULLISH",
    support: "0.8560",
    resistance: "0.8620",
    signal: "BUY",
    entry: "0.8595",
    sl: "0.8550",
    tp: "0.8640",
    probability: 68,
    pendingOrder: "BUY LIMIT 0.8580"
  },
  {
    pair: "EUR/JPY",
    price: "162.85",
    trendH4: "BULLISH",
    trendH1: "BULLISH",
    support: "162.00",
    resistance: "164.00",
    signal: "BUY",
    entry: "162.90",
    sl: "161.80",
    tp: "164.50",
    probability: 72,
    pendingOrder: "BUY STOP 163.10"
  },
  {
    pair: "GBP/JPY",
    price: "189.45",
    trendH4: "BEARISH",
    trendH1: "NEUTRAL",
    support: "188.50",
    resistance: "190.50",
    signal: "SELL",
    entry: "189.40",
    sl: "190.70",
    tp: "187.80",
    probability: 66,
    pendingOrder: "SELL LIMIT 189.60"
  }
];
