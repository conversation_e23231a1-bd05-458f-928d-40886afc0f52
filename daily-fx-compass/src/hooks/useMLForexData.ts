import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect, useState, useCallback } from 'react';
import { mlForexApi, MLPrice, MLPrediction } from '@/services/mlForexApi';
import { ForexPair } from '@/lib/data';
import { websocketService } from '@/services/websocketService';
import { userSettings } from '@/services/userSettings';

export interface MLForexData {
  prices: MLPrice[];
  predictions: MLPrediction[];
  isLoading: boolean;
  error: Error | null;
  lastUpdate: string;
  isConnected: boolean;
  wsConnected: boolean;
  forceRefresh: () => void;
  refreshPair: (symbol: string) => Promise<void>;
  updatePairWithMLData: (pair: ForexPair) => ForexPair;
}

export const useMLForexData = (updateInterval: number = 30000): MLForexData => {
  const queryClient = useQueryClient();
  const [wsConnected, setWsConnected] = useState(false);
  const [useWebSocket, setUseWebSocket] = useState(false);

  // WebSocket disabled - using HTTP polling only
  useEffect(() => {
    // Disable WebSocket for now since server doesn't support it
    setUseWebSocket(false);
    setWsConnected(false);
    websocketService.disconnect();
  }, [updateInterval, queryClient]);

  // Fetch all prices
  const pricesQuery = useQuery({
    queryKey: ['mlForexPrices'],
    queryFn: async () => {
      const result = await mlForexApi.getAllPrices();
      return result;
    },
    refetchInterval: updateInterval,
    refetchIntervalInBackground: true,
    staleTime: 0, // Always consider data stale
    gcTime: 0, // Don't cache old data (v5 renamed from cacheTime)
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
  });

  // Fetch all predictions
  const predictionsQuery = useQuery({
    queryKey: ['mlForexPredictions'],
    queryFn: async () => {
      const result = await mlForexApi.getAllPredictions();
      return result;
    },
    refetchInterval: updateInterval * 2, // Predictions update less frequently
    refetchIntervalInBackground: true,
    staleTime: 0, // Always consider data stale
    gcTime: 0, // Don't cache old data (v5 renamed from cacheTime)
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
  });

  const isConnected = !pricesQuery.isError && !predictionsQuery.isError;
  const isLoading = pricesQuery.isLoading || predictionsQuery.isLoading;
  const error = pricesQuery.error || predictionsQuery.error;

  const forceRefresh = () => {
    queryClient.invalidateQueries({ queryKey: ['mlForexPrices'] });
    queryClient.invalidateQueries({ queryKey: ['mlForexPredictions'] });
  };

  // Priority refresh for a specific pair
  const refreshPair = async (symbol: string) => {
    try {
      const normalizedSymbol = mlForexApi.normalizeSymbol(symbol);

      // Fetch fresh data for this specific pair
      const [priceData, predictionData] = await Promise.all([
        mlForexApi.getPrice(normalizedSymbol),
        mlForexApi.getPrediction(normalizedSymbol),
      ]);

      // Update cache with fresh data for this pair
      queryClient.setQueryData(['mlForexPrices'], (old: any) => {
        if (!old) return old;
        const updatedPrices = old.prices.map((p: MLPrice) =>
          p.symbol === normalizedSymbol ? priceData : p
        );
        return { ...old, prices: updatedPrices, timestamp: new Date().toISOString() };
      });

      queryClient.setQueryData(['mlForexPredictions'], (old: any) => {
        if (!old) return old;
        const updatedPredictions = old.predictions.map((p: MLPrediction) =>
          p.symbol === normalizedSymbol ? predictionData : p
        );
        return { ...old, predictions: updatedPredictions, timestamp: new Date().toISOString() };
      });
    } catch (error) {
      console.error('Error refreshing pair:', symbol, error);
    }
  };

  const updatePairWithMLData = useCallback((pair: ForexPair): ForexPair => {
    const normalizedSymbol = mlForexApi.normalizeSymbol(pair.pair);

    // Get fresh data from queries
    const prices = pricesQuery.data?.prices || [];
    const predictions = predictionsQuery.data?.predictions || [];

    // Find price data
    const priceData = prices.find((p) => p.symbol === normalizedSymbol);

    // Find prediction data
    const predictionData = predictions.find((p) => p.symbol === normalizedSymbol);

    if (!priceData) return pair;

    // Update price with real MT5 data (mid price from bid/ask)
    const isJPY = pair.pair.includes('JPY');
    const decimals = isJPY ? 2 : 4;
    const midPrice = (priceData.bid + priceData.ask) / 2;
    const newPrice = midPrice.toFixed(decimals);

    // Calculate support/resistance based on real price
    const support = (midPrice * 0.998).toFixed(decimals);
    const resistance = (midPrice * 1.002).toFixed(decimals);

    // Determine trends based on prediction if available
    let trendH1: "BULLISH" | "BEARISH" | "NEUTRAL" = pair.trendH1;
    let trendH4: "BULLISH" | "BEARISH" | "NEUTRAL" = pair.trendH4;
    let signal: "BUY" | "SELL" = pair.signal;
    let probability = pair.probability;

    if (predictionData) {
      const changePercent = predictionData.change_percent;

      // Update trends based on ML prediction
      if (changePercent > 0.05) {
        trendH1 = "BULLISH";
        trendH4 = changePercent > 0.15 ? "BULLISH" : "NEUTRAL";
        signal = "BUY";
      } else if (changePercent < -0.05) {
        trendH1 = "BEARISH";
        trendH4 = changePercent < -0.15 ? "BEARISH" : "NEUTRAL";
        signal = "SELL";
      } else {
        trendH1 = "NEUTRAL";
        trendH4 = "NEUTRAL";
      }

      // Use ML confidence as probability
      probability = Math.round(predictionData.confidence);
    }

    // Update entry/SL/TP based on signal
    const spread = priceData.spread || 0.0001;

    let entry: string;
    let sl: string;
    let tp: string;
    let pendingOrder: string;

    if (signal === "BUY") {
      entry = (midPrice + spread).toFixed(decimals);
      sl = (midPrice - (midPrice * 0.005)).toFixed(decimals);
      tp = (midPrice + (midPrice * 0.01)).toFixed(decimals);
      pendingOrder = `BUY LIMIT ${(midPrice - spread * 2).toFixed(decimals)}`;
    } else {
      entry = (midPrice - spread).toFixed(decimals);
      sl = (midPrice + (midPrice * 0.005)).toFixed(decimals);
      tp = (midPrice - (midPrice * 0.01)).toFixed(decimals);
      pendingOrder = `SELL STOP ${(midPrice - spread * 2).toFixed(decimals)}`;
    }

    return {
      ...pair,
      price: newPrice,
      support,
      resistance,
      trendH1,
      trendH4,
      signal,
      entry,
      sl,
      tp,
      probability,
      pendingOrder,
    };
  }, [pricesQuery.data, predictionsQuery.data]);

  return {
    prices: pricesQuery.data?.prices || [],
    predictions: predictionsQuery.data?.predictions || [],
    isLoading,
    error: error as Error | null,
    lastUpdate: pricesQuery.data?.timestamp || new Date().toISOString(),
    isConnected,
    wsConnected,
    forceRefresh,
    refreshPair,
    updatePairWithMLData,
  };
};

// Hook for system status
export const useMLSystemStatus = (updateInterval: number = 60000) => {
  return useQuery({
    queryKey: ['mlSystemStatus'],
    queryFn: () => mlForexApi.getSystemStatus(),
    refetchInterval: updateInterval,
    retry: 2,
  });
};

// Hook for market summary
export const useMLMarketSummary = (updateInterval: number = 60000) => {
  return useQuery({
    queryKey: ['mlMarketSummary'],
    queryFn: () => mlForexApi.getMarketSummary(),
    refetchInterval: updateInterval,
    retry: 2,
  });
};

// Hook for training status
export const useMLTrainingStatus = (updateInterval: number = 30000) => {
  return useQuery({
    queryKey: ['mlTrainingStatus'],
    queryFn: () => mlForexApi.getTrainingStatus(),
    refetchInterval: updateInterval,
    retry: 2,
  });
};
