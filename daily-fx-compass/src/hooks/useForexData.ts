import { useState, useEffect, useCallback } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { forexApi, ForexRate } from '@/services/forexApi';
import { ForexPair } from '@/lib/data';

export interface RealTimeForexData {
  rates: Record<string, ForexRate>;
  isLoading: boolean;
  error: string | null;
  lastUpdate: number;
  isConnected: boolean;
}

export const useForexData = (updateInterval: number = 30000) => {
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const queryClient = useQueryClient();

  const { data, isLoading, refetch } = useQuery({
    queryKey: ['forexRates'],
    queryFn: async () => {
      try {
        const response = await forexApi.fetchRates();
        if (!response.success) {
          throw new Error('Failed to fetch forex rates');
        }
        setError(null);
        setIsConnected(true);
        return response;
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        setIsConnected(false);
        throw err;
      }
    },
    refetchInterval: updateInterval,
    refetchIntervalInBackground: true,
    staleTime: updateInterval / 2,
  });

  const updatePairWithRealData = useCallback((pair: ForexPair): ForexPair => {
    if (!data?.data) return pair;

    const pairKey = pair.pair.replace('/', '');
    const rateData = data.data[pairKey];
    
    if (!rateData) return pair;

    // Update price and recalculate support/resistance based on real data
    const newPrice = rateData.price.toString();
    const support = (rateData.price * 0.998).toFixed(pair.pair.includes('JPY') ? 2 : 4);
    const resistance = (rateData.price * 1.002).toFixed(pair.pair.includes('JPY') ? 2 : 4);

    // Simple trend detection based on price change
    let trendH1: "BULLISH" | "BEARISH" | "NEUTRAL" = "NEUTRAL";
    let trendH4: "BULLISH" | "BEARISH" | "NEUTRAL" = "NEUTRAL";

    if (rateData.changePercent > 0.1) {
      trendH1 = "BULLISH";
      trendH4 = rateData.changePercent > 0.2 ? "BULLISH" : "NEUTRAL";
    } else if (rateData.changePercent < -0.1) {
      trendH1 = "BEARISH";
      trendH4 = rateData.changePercent < -0.2 ? "BEARISH" : "NEUTRAL";
    }

    return {
      ...pair,
      price: newPrice,
      support,
      resistance,
      trendH1,
      trendH4,
    };
  }, [data]);

  const forceRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  return {
    rates: data?.data || {},
    isLoading,
    error,
    lastUpdate: data?.timestamp || 0,
    isConnected,
    updatePairWithRealData,
    forceRefresh,
  };
};

export const useHistoricalData = (pair: string, period: '1h' | '4h' | '1d' = '1h') => {
  return useQuery({
    queryKey: ['historicalData', pair, period],
    queryFn: () => forexApi.getHistoricalRates(pair.replace('/', ''), period),
    staleTime: period === '1h' ? 300000 : period === '4h' ? 900000 : 1800000, // 5min, 15min, 30min
  });
};