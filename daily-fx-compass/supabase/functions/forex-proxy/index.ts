const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

// Currency pairs to fetch
const CURRENCY_PAIRS = [
  'EUR/USD', 'GBP/USD', 'USD/JPY', 'USD/CHF', 
  'AUD/USD', 'USD/CAD', 'NZD/USD'
];

// Free forex APIs with fallback
const APIs = [
  {
    name: 'ExchangeRate.host',
    fetch: async () => {
      console.log('Trying ExchangeRate.host API...');
      const response = await fetch('https://api.exchangerate.host/latest?base=EUR&symbols=USD,EUR,GBP,JPY,CHF,AUD,CAD,NZD');
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      const data = await response.json();
      console.log('ExchangeRate.host base:', data?.base, 'rate keys:', Object.keys(data?.rates || {}).length);
      return transformExchangeRateHost(data);
    }
  },
  {
    name: 'ExchangeRatesAPI.io',
    fetch: async () => {
      console.log('Trying ExchangeRatesAPI.io API...');
      const response = await fetch('https://api.exchangeratesapi.io/v1/latest?access_key=demo&base=USD');
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      const data = await response.json();
      return transformExchangeRatesAPI(data);
    }
  },
  {
    name: 'Fixer.io',
    fetch: async () => {
      console.log('Trying Fixer.io API...');
      const response = await fetch('https://api.fixer.io/latest?base=USD');
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      const data = await response.json();
      return transformFixerAPI(data);
    }
  }
];

function computePair(base: string, rates: Record<string, number>, baseCcy: string, quoteCcy: string) {
  const b = (base || '').toUpperCase();
  const r = rates || {};
  if (baseCcy === quoteCcy) return 1;
  const get = (ccy: string) => r[ccy];
  
  if (b === baseCcy) return get(quoteCcy);
  if (b === quoteCcy) return 1 / get(baseCcy);
  
  const baseToBaseCcy = get(baseCcy);
  const baseToQuoteCcy = get(quoteCcy);
  if (baseToBaseCcy && baseToQuoteCcy) {
    return baseToQuoteCcy / baseToBaseCcy;
  }
  return NaN;
}

function transformExchangeRateHost(data: any) {
  const rates: Record<string, any> = {};
  const timestamp = new Date().toISOString();
  const base = (data && data.base) || 'EUR';
  const raw = (data && data.rates) || {};
  // normalize keys to uppercase
  const r: Record<string, number> = {};
  for (const k in raw) {
    if (Object.prototype.hasOwnProperty.call(raw, k)) {
      r[k.toUpperCase()] = Number(raw[k]);
    }
  }
  
  const add = (key: string, a: string, b: string, label: string) => {
    const rate = computePair(base, r, a, b);
    if (Number.isFinite(rate) && !Number.isNaN(rate)) {
      rates[key] = createRate(label, rate, timestamp);
    }
  };

  add('EURUSD', 'EUR', 'USD', 'EUR/USD');
  add('GBPUSD', 'GBP', 'USD', 'GBP/USD');
  add('USDJPY', 'USD', 'JPY', 'USD/JPY');
  add('USDCHF', 'USD', 'CHF', 'USD/CHF');
  add('AUDUSD', 'AUD', 'USD', 'AUD/USD');
  add('USDCAD', 'USD', 'CAD', 'USD/CAD');
  add('NZDUSD', 'NZD', 'USD', 'NZD/USD');
  
  return { success: true, data: rates, timestamp, source: 'ExchangeRate.host' };
}

function transformExchangeRatesAPI(data: any) {
  const rates: Record<string, any> = {};
  const timestamp = new Date().toISOString();
  
  if (data.rates) {
    if (data.rates.EUR) {
      const rate = 1 / data.rates.EUR;
      rates['EURUSD'] = createRate('EUR/USD', rate, timestamp);
    }
    if (data.rates.GBP) {
      const rate = 1 / data.rates.GBP;
      rates['GBPUSD'] = createRate('GBP/USD', rate, timestamp);
    }
    if (data.rates.JPY) {
      rates['USDJPY'] = createRate('USD/JPY', data.rates.JPY, timestamp);
    }
    if (data.rates.CHF) {
      rates['USDCHF'] = createRate('USD/CHF', data.rates.CHF, timestamp);
    }
    if (data.rates.AUD) {
      const rate = 1 / data.rates.AUD;
      rates['AUDUSD'] = createRate('AUD/USD', rate, timestamp);
    }
    if (data.rates.CAD) {
      rates['USDCAD'] = createRate('USD/CAD', data.rates.CAD, timestamp);
    }
    if (data.rates.NZD) {
      const rate = 1 / data.rates.NZD;
      rates['NZDUSD'] = createRate('NZD/USD', rate, timestamp);
    }
  }
  
  return { success: true, data: rates, timestamp, source: 'ExchangeRatesAPI.io' };
}

function transformFixerAPI(data: any) {
  return transformExchangeRateHost(data);
}

function createRate(pair: string, rate: number, timestamp: string) {
  const bid = rate * 0.9999;
  const ask = rate * 1.0001;
  
  return {
    pair,
    bid: Number(bid.toFixed(5)),
    ask: Number(ask.toFixed(5)),
    price: Number(rate.toFixed(5)),
    change: 0,
    changePercent: 0,
    timestamp
  };
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log('Forex proxy request received');
    
    // Try each API in sequence until one works
    let lastError = null;
    for (const api of APIs) {
      try {
        const data = await api.fetch();
        console.log(`Successfully fetched data from ${api.name}`);
        
        return new Response(
          JSON.stringify(data),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 200,
          }
        );
      } catch (error) {
        console.warn(`${api.name} failed:`, error.message);
        lastError = error;
        continue;
      }
    }
    
    // If all APIs failed, throw the last error
    throw lastError || new Error('All forex APIs failed');
    
  } catch (error) {
    console.error('All forex APIs failed:', error);
    
    return new Response(
      JSON.stringify({ 
        error: error.message,
        success: false,
        message: 'All forex data sources are currently unavailable'
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});
